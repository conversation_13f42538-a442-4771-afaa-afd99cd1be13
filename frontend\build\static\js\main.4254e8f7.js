/*! For license information please see main.4254e8f7.js.LICENSE.txt */
(()=>{"use strict";var e={4:(e,t,n)=>{var r=n(853),o=n(43),a=n(950);function l(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function i(e){return!(!e||1!==e.nodeType&&9!==e.nodeType&&11!==e.nodeType)}var s=Symbol.for("react.element"),c=Symbol.for("react.transitional.element"),u=Symbol.for("react.portal"),d=Symbol.for("react.fragment"),f=Symbol.for("react.strict_mode"),p=Symbol.for("react.profiler"),m=Symbol.for("react.provider"),h=Symbol.for("react.consumer"),g=Symbol.for("react.context"),v=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),b=Symbol.for("react.suspense_list"),w=Symbol.for("react.memo"),x=Symbol.for("react.lazy");Symbol.for("react.scope"),Symbol.for("react.debug_trace_mode");var k=Symbol.for("react.offscreen");Symbol.for("react.legacy_hidden"),Symbol.for("react.tracing_marker");var S=Symbol.for("react.memo_cache_sentinel"),N=Symbol.iterator;function E(e){return null===e||"object"!==typeof e?null:"function"===typeof(e=N&&e[N]||e["@@iterator"])?e:null}var C=Symbol.for("react.client.reference");function j(e){if(null==e)return null;if("function"===typeof e)return e.$$typeof===C?null:e.displayName||e.name||null;if("string"===typeof e)return e;switch(e){case d:return"Fragment";case u:return"Portal";case p:return"Profiler";case f:return"StrictMode";case y:return"Suspense";case b:return"SuspenseList"}if("object"===typeof e)switch(e.$$typeof){case g:return(e.displayName||"Context")+".Provider";case h:return(e._context.displayName||"Context")+".Consumer";case v:var t=e.render;return(e=e.displayName)||(e=""!==(e=t.displayName||t.name||"")?"ForwardRef("+e+")":"ForwardRef"),e;case w:return null!==(t=e.displayName||null)?t:j(e.type)||"Memo";case x:t=e._payload,e=e._init;try{return j(e(t))}catch(n){}}return null}var P,_,T=o.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,R=Object.assign;function A(e){if(void 0===P)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);P=t&&t[1]||"",_=-1<n.stack.indexOf("\n    at")?" (<anonymous>)":-1<n.stack.indexOf("@")?"@unknown:0:0":""}return"\n"+P+e+_}var z=!1;function O(e,t){if(!e||z)return"";z=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var r={DetermineComponentFrameRoot:function(){try{if(t){var n=function(){throw Error()};if(Object.defineProperty(n.prototype,"props",{set:function(){throw Error()}}),"object"===typeof Reflect&&Reflect.construct){try{Reflect.construct(n,[])}catch(o){var r=o}Reflect.construct(e,[],n)}else{try{n.call()}catch(a){r=a}e.call(n.prototype)}}else{try{throw Error()}catch(l){r=l}(n=e())&&"function"===typeof n.catch&&n.catch((function(){}))}}catch(i){if(i&&r&&"string"===typeof i.stack)return[i.stack,r.stack]}return[null,null]}};r.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var o=Object.getOwnPropertyDescriptor(r.DetermineComponentFrameRoot,"name");o&&o.configurable&&Object.defineProperty(r.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var a=r.DetermineComponentFrameRoot(),l=a[0],i=a[1];if(l&&i){var s=l.split("\n"),c=i.split("\n");for(o=r=0;r<s.length&&!s[r].includes("DetermineComponentFrameRoot");)r++;for(;o<c.length&&!c[o].includes("DetermineComponentFrameRoot");)o++;if(r===s.length||o===c.length)for(r=s.length-1,o=c.length-1;1<=r&&0<=o&&s[r]!==c[o];)o--;for(;1<=r&&0<=o;r--,o--)if(s[r]!==c[o]){if(1!==r||1!==o)do{if(r--,0>--o||s[r]!==c[o]){var u="\n"+s[r].replace(" at new "," at ");return e.displayName&&u.includes("<anonymous>")&&(u=u.replace("<anonymous>",e.displayName)),u}}while(1<=r&&0<=o);break}}}finally{z=!1,Error.prepareStackTrace=n}return(n=e?e.displayName||e.name:"")?A(n):""}function L(e){switch(e.tag){case 26:case 27:case 5:return A(e.type);case 16:return A("Lazy");case 13:return A("Suspense");case 19:return A("SuspenseList");case 0:case 15:return e=O(e.type,!1);case 11:return e=O(e.type.render,!1);case 1:return e=O(e.type,!0);default:return""}}function M(e){try{var t="";do{t+=L(e),e=e.return}while(e);return t}catch(n){return"\nError generating stack: "+n.message+"\n"+n.stack}}function D(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do{0!==(4098&(t=e).flags)&&(n=t.return),e=t.return}while(e)}return 3===t.tag?n:null}function I(e){if(13===e.tag){var t=e.memoizedState;if(null===t&&(null!==(e=e.alternate)&&(t=e.memoizedState)),null!==t)return t.dehydrated}return null}function F(e){if(D(e)!==e)throw Error(l(188))}function H(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e;for(e=e.child;null!==e;){if(null!==(t=H(e)))return t;e=e.sibling}return null}var U=Array.isArray,W=a.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,V={pending:!1,data:null,method:null,action:null},B=[],$=-1;function q(e){return{current:e}}function K(e){0>$||(e.current=B[$],B[$]=null,$--)}function Q(e,t){$++,B[$]=e.current,e.current=t}var G=q(null),Y=q(null),X=q(null),Z=q(null);function J(e,t){switch(Q(X,t),Q(Y,e),Q(G,null),e=t.nodeType){case 9:case 11:t=(t=t.documentElement)&&(t=t.namespaceURI)?Yu(t):0;break;default:if(t=(e=8===e?t.parentNode:t).tagName,e=e.namespaceURI)t=Xu(e=Yu(e),t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}K(G),Q(G,t)}function ee(){K(G),K(Y),K(X)}function te(e){null!==e.memoizedState&&Q(Z,e);var t=G.current,n=Xu(t,e.type);t!==n&&(Q(Y,e),Q(G,n))}function ne(e){Y.current===e&&(K(G),K(Y)),Z.current===e&&(K(Z),Md._currentValue=V)}var re=Object.prototype.hasOwnProperty,oe=r.unstable_scheduleCallback,ae=r.unstable_cancelCallback,le=r.unstable_shouldYield,ie=r.unstable_requestPaint,se=r.unstable_now,ce=r.unstable_getCurrentPriorityLevel,ue=r.unstable_ImmediatePriority,de=r.unstable_UserBlockingPriority,fe=r.unstable_NormalPriority,pe=r.unstable_LowPriority,me=r.unstable_IdlePriority,he=r.log,ge=r.unstable_setDisableYieldValue,ve=null,ye=null;function be(e){if("function"===typeof he&&ge(e),ye&&"function"===typeof ye.setStrictMode)try{ye.setStrictMode(ve,e)}catch(t){}}var we=Math.clz32?Math.clz32:function(e){return 0===(e>>>=0)?32:31-(xe(e)/ke|0)|0},xe=Math.log,ke=Math.LN2;var Se=128,Ne=4194304;function Ee(e){var t=42&e;if(0!==t)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return 4194176&e;case 4194304:case 8388608:case 16777216:case 33554432:return 62914560&e;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Ce(e,t){var n=e.pendingLanes;if(0===n)return 0;var r=0,o=e.suspendedLanes,a=e.pingedLanes,l=e.warmLanes;e=0!==e.finishedLanes;var i=134217727&n;return 0!==i?0!==(n=i&~o)?r=Ee(n):0!==(a&=i)?r=Ee(a):e||0!==(l=i&~l)&&(r=Ee(l)):0!==(i=n&~o)?r=Ee(i):0!==a?r=Ee(a):e||0!==(l=n&~l)&&(r=Ee(l)),0===r?0:0!==t&&t!==r&&0===(t&o)&&((o=r&-r)>=(l=t&-t)||32===o&&0!==(4194176&l))?t:r}function je(e,t){return 0===(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)}function Pe(e,t){switch(e){case 1:case 2:case 4:case 8:return t+250;case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;default:return-1}}function _e(){var e=Se;return 0===(4194176&(Se<<=1))&&(Se=128),e}function Te(){var e=Ne;return 0===(62914560&(Ne<<=1))&&(Ne=4194304),e}function Re(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Ae(e,t){e.pendingLanes|=t,268435456!==t&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function ze(e,t,n){e.pendingLanes|=t,e.suspendedLanes&=~t;var r=31-we(t);e.entangledLanes|=t,e.entanglements[r]=1073741824|e.entanglements[r]|4194218&n}function Oe(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-we(n),o=1<<r;o&t|e[r]&t&&(e[r]|=t),n&=~o}}function Le(e){return 2<(e&=-e)?8<e?0!==(134217727&e)?32:268435456:8:2}function Me(){var e=W.p;return 0!==e?e:void 0===(e=window.event)?32:Xd(e.type)}var De=Math.random().toString(36).slice(2),Ie="__reactFiber$"+De,Fe="__reactProps$"+De,He="__reactContainer$"+De,Ue="__reactEvents$"+De,We="__reactListeners$"+De,Ve="__reactHandles$"+De,Be="__reactResources$"+De,$e="__reactMarker$"+De;function qe(e){delete e[Ie],delete e[Fe],delete e[Ue],delete e[We],delete e[Ve]}function Ke(e){var t=e[Ie];if(t)return t;for(var n=e.parentNode;n;){if(t=n[He]||n[Ie]){if(n=t.alternate,null!==t.child||null!==n&&null!==n.child)for(e=sd(e);null!==e;){if(n=e[Ie])return n;e=sd(e)}return t}n=(e=n).parentNode}return null}function Qe(e){if(e=e[Ie]||e[He]){var t=e.tag;if(5===t||6===t||13===t||26===t||27===t||3===t)return e}return null}function Ge(e){var t=e.tag;if(5===t||26===t||27===t||6===t)return e.stateNode;throw Error(l(33))}function Ye(e){var t=e[Be];return t||(t=e[Be]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Xe(e){e[$e]=!0}var Ze=new Set,Je={};function et(e,t){tt(e,t),tt(e+"Capture",t)}function tt(e,t){for(Je[e]=t,e=0;e<t.length;e++)Ze.add(t[e])}var nt=!("undefined"===typeof window||"undefined"===typeof window.document||"undefined"===typeof window.document.createElement),rt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ot={},at={};function lt(e,t,n){if(o=t,re.call(at,o)||!re.call(ot,o)&&(rt.test(o)?at[o]=!0:(ot[o]=!0,0)))if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":return void e.removeAttribute(t);case"boolean":var r=t.toLowerCase().slice(0,5);if("data-"!==r&&"aria-"!==r)return void e.removeAttribute(t)}e.setAttribute(t,""+n)}var o}function it(e,t,n){if(null===n)e.removeAttribute(t);else{switch(typeof n){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(t)}e.setAttribute(t,""+n)}}function st(e,t,n,r){if(null===r)e.removeAttribute(n);else{switch(typeof r){case"undefined":case"function":case"symbol":case"boolean":return void e.removeAttribute(n)}e.setAttributeNS(t,n,""+r)}}function ct(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":case"object":return e;default:return""}}function ut(e){var t=e.type;return(e=e.nodeName)&&"input"===e.toLowerCase()&&("checkbox"===t||"radio"===t)}function dt(e){e._valueTracker||(e._valueTracker=function(e){var t=ut(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&"undefined"!==typeof n&&"function"===typeof n.get&&"function"===typeof n.set){var o=n.get,a=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return o.call(this)},set:function(e){r=""+e,a.call(this,e)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(e){r=""+e},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}(e))}function ft(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=ut(e)?e.checked?"true":"false":e.value),(e=r)!==n&&(t.setValue(e),!0)}function pt(e){if("undefined"===typeof(e=e||("undefined"!==typeof document?document:void 0)))return null;try{return e.activeElement||e.body}catch(t){return e.body}}var mt=/[\n"\\]/g;function ht(e){return e.replace(mt,(function(e){return"\\"+e.charCodeAt(0).toString(16)+" "}))}function gt(e,t,n,r,o,a,l,i){e.name="",null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l?e.type=l:e.removeAttribute("type"),null!=t?"number"===l?(0===t&&""===e.value||e.value!=t)&&(e.value=""+ct(t)):e.value!==""+ct(t)&&(e.value=""+ct(t)):"submit"!==l&&"reset"!==l||e.removeAttribute("value"),null!=t?yt(e,l,ct(t)):null!=n?yt(e,l,ct(n)):null!=r&&e.removeAttribute("value"),null==o&&null!=a&&(e.defaultChecked=!!a),null!=o&&(e.checked=o&&"function"!==typeof o&&"symbol"!==typeof o),null!=i&&"function"!==typeof i&&"symbol"!==typeof i&&"boolean"!==typeof i?e.name=""+ct(i):e.removeAttribute("name")}function vt(e,t,n,r,o,a,l,i){if(null!=a&&"function"!==typeof a&&"symbol"!==typeof a&&"boolean"!==typeof a&&(e.type=a),null!=t||null!=n){if(!("submit"!==a&&"reset"!==a||void 0!==t&&null!==t))return;n=null!=n?""+ct(n):"",t=null!=t?""+ct(t):n,i||t===e.value||(e.value=t),e.defaultValue=t}r="function"!==typeof(r=null!=r?r:o)&&"symbol"!==typeof r&&!!r,e.checked=i?e.checked:!!r,e.defaultChecked=!!r,null!=l&&"function"!==typeof l&&"symbol"!==typeof l&&"boolean"!==typeof l&&(e.name=l)}function yt(e,t,n){"number"===t&&pt(e.ownerDocument)===e||e.defaultValue===""+n||(e.defaultValue=""+n)}function bt(e,t,n,r){if(e=e.options,t){t={};for(var o=0;o<n.length;o++)t["$"+n[o]]=!0;for(n=0;n<e.length;n++)o=t.hasOwnProperty("$"+e[n].value),e[n].selected!==o&&(e[n].selected=o),o&&r&&(e[n].defaultSelected=!0)}else{for(n=""+ct(n),t=null,o=0;o<e.length;o++){if(e[o].value===n)return e[o].selected=!0,void(r&&(e[o].defaultSelected=!0));null!==t||e[o].disabled||(t=e[o])}null!==t&&(t.selected=!0)}}function wt(e,t,n){null==t||((t=""+ct(t))!==e.value&&(e.value=t),null!=n)?e.defaultValue=null!=n?""+ct(n):"":e.defaultValue!==t&&(e.defaultValue=t)}function xt(e,t,n,r){if(null==t){if(null!=r){if(null!=n)throw Error(l(92));if(U(r)){if(1<r.length)throw Error(l(93));r=r[0]}n=r}null==n&&(n=""),t=n}n=ct(t),e.defaultValue=n,(r=e.textContent)===n&&""!==r&&null!==r&&(e.value=r)}function kt(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&3===n.nodeType)return void(n.nodeValue=t)}e.textContent=t}var St=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Nt(e,t,n){var r=0===t.indexOf("--");null==n||"boolean"===typeof n||""===n?r?e.setProperty(t,""):"float"===t?e.cssFloat="":e[t]="":r?e.setProperty(t,n):"number"!==typeof n||0===n||St.has(t)?"float"===t?e.cssFloat=n:e[t]=(""+n).trim():e[t]=n+"px"}function Et(e,t,n){if(null!=t&&"object"!==typeof t)throw Error(l(62));if(e=e.style,null!=n){for(var r in n)!n.hasOwnProperty(r)||null!=t&&t.hasOwnProperty(r)||(0===r.indexOf("--")?e.setProperty(r,""):"float"===r?e.cssFloat="":e[r]="");for(var o in t)r=t[o],t.hasOwnProperty(o)&&n[o]!==r&&Nt(e,o,r)}else for(var a in t)t.hasOwnProperty(a)&&Nt(e,a,t[a])}function Ct(e){if(-1===e.indexOf("-"))return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var jt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pt=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function _t(e){return Pt.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var Tt=null;function Rt(e){return(e=e.target||e.srcElement||window).correspondingUseElement&&(e=e.correspondingUseElement),3===e.nodeType?e.parentNode:e}var At=null,zt=null;function Ot(e){var t=Qe(e);if(t&&(e=t.stateNode)){var n=e[Fe]||null;e:switch(e=t.stateNode,t.type){case"input":if(gt(e,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name),t=n.name,"radio"===n.type&&null!=t){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll('input[name="'+ht(""+t)+'"][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var o=r[Fe]||null;if(!o)throw Error(l(90));gt(r,o.value,o.defaultValue,o.defaultValue,o.checked,o.defaultChecked,o.type,o.name)}}for(t=0;t<n.length;t++)(r=n[t]).form===e.form&&ft(r)}break e;case"textarea":wt(e,n.value,n.defaultValue);break e;case"select":null!=(t=n.value)&&bt(e,!!n.multiple,t,!1)}}}var Lt=!1;function Mt(e,t,n){if(Lt)return e(t,n);Lt=!0;try{return e(t)}finally{if(Lt=!1,(null!==At||null!==zt)&&(Ic(),At&&(t=At,e=zt,zt=At=null,Ot(t),e)))for(t=0;t<e.length;t++)Ot(e[t])}}function Dt(e,t){var n=e.stateNode;if(null===n)return null;var r=n[Fe]||null;if(null===r)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(r=!("button"===(e=e.type)||"input"===e||"select"===e||"textarea"===e)),e=!r;break e;default:e=!1}if(e)return null;if(n&&"function"!==typeof n)throw Error(l(231,t,typeof n));return n}var It=!1;if(nt)try{var Ft={};Object.defineProperty(Ft,"passive",{get:function(){It=!0}}),window.addEventListener("test",Ft,Ft),window.removeEventListener("test",Ft,Ft)}catch(kf){It=!1}var Ht=null,Ut=null,Wt=null;function Vt(){if(Wt)return Wt;var e,t,n=Ut,r=n.length,o="value"in Ht?Ht.value:Ht.textContent,a=o.length;for(e=0;e<r&&n[e]===o[e];e++);var l=r-e;for(t=1;t<=l&&n[r-t]===o[a-t];t++);return Wt=o.slice(e,1<t?1-t:void 0)}function Bt(e){var t=e.keyCode;return"charCode"in e?0===(e=e.charCode)&&13===t&&(e=13):e=t,10===e&&(e=13),32<=e||13===e?e:0}function $t(){return!0}function qt(){return!1}function Kt(e){function t(t,n,r,o,a){for(var l in this._reactName=t,this._targetInst=r,this.type=n,this.nativeEvent=o,this.target=a,this.currentTarget=null,e)e.hasOwnProperty(l)&&(t=e[l],this[l]=t?t(o):o[l]);return this.isDefaultPrevented=(null!=o.defaultPrevented?o.defaultPrevented:!1===o.returnValue)?$t:qt,this.isPropagationStopped=qt,this}return R(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var e=this.nativeEvent;e&&(e.preventDefault?e.preventDefault():"unknown"!==typeof e.returnValue&&(e.returnValue=!1),this.isDefaultPrevented=$t)},stopPropagation:function(){var e=this.nativeEvent;e&&(e.stopPropagation?e.stopPropagation():"unknown"!==typeof e.cancelBubble&&(e.cancelBubble=!0),this.isPropagationStopped=$t)},persist:function(){},isPersistent:$t}),t}var Qt,Gt,Yt,Xt={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Zt=Kt(Xt),Jt=R({},Xt,{view:0,detail:0}),en=Kt(Jt),tn=R({},Jt,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:pn,button:0,buttons:0,relatedTarget:function(e){return void 0===e.relatedTarget?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==Yt&&(Yt&&"mousemove"===e.type?(Qt=e.screenX-Yt.screenX,Gt=e.screenY-Yt.screenY):Gt=Qt=0,Yt=e),Qt)},movementY:function(e){return"movementY"in e?e.movementY:Gt}}),nn=Kt(tn),rn=Kt(R({},tn,{dataTransfer:0})),on=Kt(R({},Jt,{relatedTarget:0})),an=Kt(R({},Xt,{animationName:0,elapsedTime:0,pseudoElement:0})),ln=Kt(R({},Xt,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}})),sn=Kt(R({},Xt,{data:0})),cn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},un={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},dn={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function fn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):!!(e=dn[e])&&!!t[e]}function pn(){return fn}var mn=Kt(R({},Jt,{key:function(e){if(e.key){var t=cn[e.key]||e.key;if("Unidentified"!==t)return t}return"keypress"===e.type?13===(e=Bt(e))?"Enter":String.fromCharCode(e):"keydown"===e.type||"keyup"===e.type?un[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:pn,charCode:function(e){return"keypress"===e.type?Bt(e):0},keyCode:function(e){return"keydown"===e.type||"keyup"===e.type?e.keyCode:0},which:function(e){return"keypress"===e.type?Bt(e):"keydown"===e.type||"keyup"===e.type?e.keyCode:0}})),hn=Kt(R({},tn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0})),gn=Kt(R({},Jt,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:pn})),vn=Kt(R({},Xt,{propertyName:0,elapsedTime:0,pseudoElement:0})),yn=Kt(R({},tn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0})),bn=Kt(R({},Xt,{newState:0,oldState:0})),wn=[9,13,27,32],xn=nt&&"CompositionEvent"in window,kn=null;nt&&"documentMode"in document&&(kn=document.documentMode);var Sn=nt&&"TextEvent"in window&&!kn,Nn=nt&&(!xn||kn&&8<kn&&11>=kn),En=String.fromCharCode(32),Cn=!1;function jn(e,t){switch(e){case"keyup":return-1!==wn.indexOf(t.keyCode);case"keydown":return 229!==t.keyCode;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Pn(e){return"object"===typeof(e=e.detail)&&"data"in e?e.data:null}var _n=!1;var Tn={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Rn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return"input"===t?!!Tn[e.type]:"textarea"===t}function An(e,t,n,r){At?zt?zt.push(r):zt=[r]:At=r,0<(t=Mu(t,"onChange")).length&&(n=new Zt("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var zn=null,On=null;function Ln(e){Pu(e,0)}function Mn(e){if(ft(Ge(e)))return e}function Dn(e,t){if("change"===e)return t}var In=!1;if(nt){var Fn;if(nt){var Hn="oninput"in document;if(!Hn){var Un=document.createElement("div");Un.setAttribute("oninput","return;"),Hn="function"===typeof Un.oninput}Fn=Hn}else Fn=!1;In=Fn&&(!document.documentMode||9<document.documentMode)}function Wn(){zn&&(zn.detachEvent("onpropertychange",Vn),On=zn=null)}function Vn(e){if("value"===e.propertyName&&Mn(On)){var t=[];An(t,On,e,Rt(e)),Mt(Ln,t)}}function Bn(e,t,n){"focusin"===e?(Wn(),On=n,(zn=t).attachEvent("onpropertychange",Vn)):"focusout"===e&&Wn()}function $n(e){if("selectionchange"===e||"keyup"===e||"keydown"===e)return Mn(On)}function qn(e,t){if("click"===e)return Mn(t)}function Kn(e,t){if("input"===e||"change"===e)return Mn(t)}var Qn="function"===typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e===1/t)||e!==e&&t!==t};function Gn(e,t){if(Qn(e,t))return!0;if("object"!==typeof e||null===e||"object"!==typeof t||null===t)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var o=n[r];if(!re.call(t,o)||!Qn(e[o],t[o]))return!1}return!0}function Yn(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Xn(e,t){var n,r=Yn(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&n>=t)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=Yn(r)}}function Zn(e,t){return!(!e||!t)&&(e===t||(!e||3!==e.nodeType)&&(t&&3===t.nodeType?Zn(e,t.parentNode):"contains"in e?e.contains(t):!!e.compareDocumentPosition&&!!(16&e.compareDocumentPosition(t))))}function Jn(e){for(var t=pt((e=null!=e&&null!=e.ownerDocument&&null!=e.ownerDocument.defaultView?e.ownerDocument.defaultView:window).document);t instanceof e.HTMLIFrameElement;){try{var n="string"===typeof t.contentWindow.location.href}catch(r){n=!1}if(!n)break;t=pt((e=t.contentWindow).document)}return t}function er(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&("input"===t&&("text"===e.type||"search"===e.type||"tel"===e.type||"url"===e.type||"password"===e.type)||"textarea"===t||"true"===e.contentEditable)}function tr(e,t){var n=Jn(t);t=e.focusedElem;var r=e.selectionRange;if(n!==t&&t&&t.ownerDocument&&Zn(t.ownerDocument.documentElement,t)){if(null!==r&&er(t))if(e=r.start,void 0===(n=r.end)&&(n=e),"selectionStart"in t)t.selectionStart=e,t.selectionEnd=Math.min(n,t.value.length);else if((n=(e=t.ownerDocument||document)&&e.defaultView||window).getSelection){n=n.getSelection();var o=t.textContent.length,a=Math.min(r.start,o);r=void 0===r.end?a:Math.min(r.end,o),!n.extend&&a>r&&(o=r,r=a,a=o),o=Xn(t,a);var l=Xn(t,r);o&&l&&(1!==n.rangeCount||n.anchorNode!==o.node||n.anchorOffset!==o.offset||n.focusNode!==l.node||n.focusOffset!==l.offset)&&((e=e.createRange()).setStart(o.node,o.offset),n.removeAllRanges(),a>r?(n.addRange(e),n.extend(l.node,l.offset)):(e.setEnd(l.node,l.offset),n.addRange(e)))}for(e=[],n=t;n=n.parentNode;)1===n.nodeType&&e.push({element:n,left:n.scrollLeft,top:n.scrollTop});for("function"===typeof t.focus&&t.focus(),t=0;t<e.length;t++)(n=e[t]).element.scrollLeft=n.left,n.element.scrollTop=n.top}}var nr=nt&&"documentMode"in document&&11>=document.documentMode,rr=null,or=null,ar=null,lr=!1;function ir(e,t,n){var r=n.window===n?n.document:9===n.nodeType?n:n.ownerDocument;lr||null==rr||rr!==pt(r)||("selectionStart"in(r=rr)&&er(r)?r={start:r.selectionStart,end:r.selectionEnd}:r={anchorNode:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection()).anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset},ar&&Gn(ar,r)||(ar=r,0<(r=Mu(or,"onSelect")).length&&(t=new Zt("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=rr)))}function sr(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var cr={animationend:sr("Animation","AnimationEnd"),animationiteration:sr("Animation","AnimationIteration"),animationstart:sr("Animation","AnimationStart"),transitionrun:sr("Transition","TransitionRun"),transitionstart:sr("Transition","TransitionStart"),transitioncancel:sr("Transition","TransitionCancel"),transitionend:sr("Transition","TransitionEnd")},ur={},dr={};function fr(e){if(ur[e])return ur[e];if(!cr[e])return e;var t,n=cr[e];for(t in n)if(n.hasOwnProperty(t)&&t in dr)return ur[e]=n[t];return e}nt&&(dr=document.createElement("div").style,"AnimationEvent"in window||(delete cr.animationend.animation,delete cr.animationiteration.animation,delete cr.animationstart.animation),"TransitionEvent"in window||delete cr.transitionend.transition);var pr=fr("animationend"),mr=fr("animationiteration"),hr=fr("animationstart"),gr=fr("transitionrun"),vr=fr("transitionstart"),yr=fr("transitioncancel"),br=fr("transitionend"),wr=new Map,xr="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll scrollEnd toggle touchMove waiting wheel".split(" ");function kr(e,t){wr.set(e,t),et(t,[e])}var Sr=[],Nr=0,Er=0;function Cr(){for(var e=Nr,t=Er=Nr=0;t<e;){var n=Sr[t];Sr[t++]=null;var r=Sr[t];Sr[t++]=null;var o=Sr[t];Sr[t++]=null;var a=Sr[t];if(Sr[t++]=null,null!==r&&null!==o){var l=r.pending;null===l?o.next=o:(o.next=l.next,l.next=o),r.pending=o}0!==a&&Tr(n,o,a)}}function jr(e,t,n,r){Sr[Nr++]=e,Sr[Nr++]=t,Sr[Nr++]=n,Sr[Nr++]=r,Er|=r,e.lanes|=r,null!==(e=e.alternate)&&(e.lanes|=r)}function Pr(e,t,n,r){return jr(e,t,n,r),Rr(e)}function _r(e,t){return jr(e,null,null,t),Rr(e)}function Tr(e,t,n){e.lanes|=n;var r=e.alternate;null!==r&&(r.lanes|=n);for(var o=!1,a=e.return;null!==a;)a.childLanes|=n,null!==(r=a.alternate)&&(r.childLanes|=n),22===a.tag&&(null===(e=a.stateNode)||1&e._visibility||(o=!0)),e=a,a=a.return;o&&null!==t&&3===e.tag&&(a=e.stateNode,o=31-we(n),null===(e=(a=a.hiddenUpdates)[o])?a[o]=[t]:e.push(t),t.lane=536870912|n)}function Rr(e){if(50<Pc)throw Pc=0,_c=null,Error(l(185));for(var t=e.return;null!==t;)t=(e=t).return;return 3===e.tag?e.stateNode:null}var Ar={},zr=new WeakMap;function Or(e,t){if("object"===typeof e&&null!==e){var n=zr.get(e);return void 0!==n?n:(t={value:e,source:t,stack:M(t)},zr.set(e,t),t)}return{value:e,source:t,stack:M(t)}}var Lr=[],Mr=0,Dr=null,Ir=0,Fr=[],Hr=0,Ur=null,Wr=1,Vr="";function Br(e,t){Lr[Mr++]=Ir,Lr[Mr++]=Dr,Dr=e,Ir=t}function $r(e,t,n){Fr[Hr++]=Wr,Fr[Hr++]=Vr,Fr[Hr++]=Ur,Ur=e;var r=Wr;e=Vr;var o=32-we(r)-1;r&=~(1<<o),n+=1;var a=32-we(t)+o;if(30<a){var l=o-o%5;a=(r&(1<<l)-1).toString(32),r>>=l,o-=l,Wr=1<<32-we(t)+o|n<<o|r,Vr=a+e}else Wr=1<<a|n<<o|r,Vr=e}function qr(e){null!==e.return&&(Br(e,1),$r(e,1,0))}function Kr(e){for(;e===Dr;)Dr=Lr[--Mr],Lr[Mr]=null,Ir=Lr[--Mr],Lr[Mr]=null;for(;e===Ur;)Ur=Fr[--Hr],Fr[Hr]=null,Vr=Fr[--Hr],Fr[Hr]=null,Wr=Fr[--Hr],Fr[Hr]=null}var Qr=null,Gr=null,Yr=!1,Xr=null,Zr=!1,Jr=Error(l(519));function eo(e){throw ao(Or(Error(l(418,"")),e)),Jr}function to(e){var t=e.stateNode,n=e.type,r=e.memoizedProps;switch(t[Ie]=e,t[Fe]=r,n){case"dialog":_u("cancel",t),_u("close",t);break;case"iframe":case"object":case"embed":_u("load",t);break;case"video":case"audio":for(n=0;n<Cu.length;n++)_u(Cu[n],t);break;case"source":_u("error",t);break;case"img":case"image":case"link":_u("error",t),_u("load",t);break;case"details":_u("toggle",t);break;case"input":_u("invalid",t),vt(t,r.value,r.defaultValue,r.checked,r.defaultChecked,r.type,r.name,!0),dt(t);break;case"select":_u("invalid",t);break;case"textarea":_u("invalid",t),xt(t,r.value,r.defaultValue,r.children),dt(t)}"string"!==typeof(n=r.children)&&"number"!==typeof n&&"bigint"!==typeof n||t.textContent===""+n||!0===r.suppressHydrationWarning||Wu(t.textContent,n)?(null!=r.popover&&(_u("beforetoggle",t),_u("toggle",t)),null!=r.onScroll&&_u("scroll",t),null!=r.onScrollEnd&&_u("scrollend",t),null!=r.onClick&&(t.onclick=Vu),t=!0):t=!1,t||eo(e)}function no(e){for(Qr=e.return;Qr;)switch(Qr.tag){case 3:case 27:return void(Zr=!0);case 5:case 13:return void(Zr=!1);default:Qr=Qr.return}}function ro(e){if(e!==Qr)return!1;if(!Yr)return no(e),Yr=!0,!1;var t,n=!1;if((t=3!==e.tag&&27!==e.tag)&&((t=5===e.tag)&&(t=!("form"!==(t=e.type)&&"button"!==t)||Zu(e.type,e.memoizedProps)),t=!t),t&&(n=!0),n&&Gr&&eo(e),no(e),13===e.tag){if(!(e=null!==(e=e.memoizedState)?e.dehydrated:null))throw Error(l(317));e:{for(e=e.nextSibling,n=0;e;){if(8===e.nodeType)if("/$"===(t=e.data)){if(0===n){Gr=id(e.nextSibling);break e}n--}else"$"!==t&&"$!"!==t&&"$?"!==t||n++;e=e.nextSibling}Gr=null}}else Gr=Qr?id(e.stateNode.nextSibling):null;return!0}function oo(){Gr=Qr=null,Yr=!1}function ao(e){null===Xr?Xr=[e]:Xr.push(e)}var lo=Error(l(460)),io=Error(l(474)),so={then:function(){}};function co(e){return"fulfilled"===(e=e.status)||"rejected"===e}function uo(){}function fo(e,t,n){switch(void 0===(n=e[n])?e.push(t):n!==t&&(t.then(uo,uo),t=n),t.status){case"fulfilled":return t.value;case"rejected":if((e=t.reason)===lo)throw Error(l(483));throw e;default:if("string"===typeof t.status)t.then(uo,uo);else{if(null!==(e=tc)&&100<e.shellSuspendCounter)throw Error(l(482));(e=t).status="pending",e.then((function(e){if("pending"===t.status){var n=t;n.status="fulfilled",n.value=e}}),(function(e){if("pending"===t.status){var n=t;n.status="rejected",n.reason=e}}))}switch(t.status){case"fulfilled":return t.value;case"rejected":if((e=t.reason)===lo)throw Error(l(483));throw e}throw po=t,lo}}var po=null;function mo(){if(null===po)throw Error(l(459));var e=po;return po=null,e}var ho=null,go=0;function vo(e){var t=go;return go+=1,null===ho&&(ho=[]),fo(ho,e,t)}function yo(e,t){t=t.props.ref,e.ref=void 0!==t?t:null}function bo(e,t){if(t.$$typeof===s)throw Error(l(525));throw e=Object.prototype.toString.call(t),Error(l(31,"[object Object]"===e?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function wo(e){return(0,e._init)(e._payload)}function xo(e){function t(t,n){if(e){var r=t.deletions;null===r?(t.deletions=[n],t.flags|=16):r.push(n)}}function n(n,r){if(!e)return null;for(;null!==r;)t(n,r),r=r.sibling;return null}function r(e){for(var t=new Map;null!==e;)null!==e.key?t.set(e.key,e):t.set(e.index,e),e=e.sibling;return t}function o(e,t){return(e=Ds(e,t)).index=0,e.sibling=null,e}function a(t,n,r){return t.index=r,e?null!==(r=t.alternate)?(r=r.index)<n?(t.flags|=33554434,n):r:(t.flags|=33554434,n):(t.flags|=1048576,n)}function i(t){return e&&null===t.alternate&&(t.flags|=33554434),t}function s(e,t,n,r){return null===t||6!==t.tag?((t=Ws(n,e.mode,r)).return=e,t):((t=o(t,n)).return=e,t)}function f(e,t,n,r){var a=n.type;return a===d?m(e,t,n.props.children,r,n.key):null!==t&&(t.elementType===a||"object"===typeof a&&null!==a&&a.$$typeof===x&&wo(a)===t.type)?(yo(t=o(t,n.props),n),t.return=e,t):(yo(t=Fs(n.type,n.key,n.props,null,e.mode,r),n),t.return=e,t)}function p(e,t,n,r){return null===t||4!==t.tag||t.stateNode.containerInfo!==n.containerInfo||t.stateNode.implementation!==n.implementation?((t=Vs(n,e.mode,r)).return=e,t):((t=o(t,n.children||[])).return=e,t)}function m(e,t,n,r,a){return null===t||7!==t.tag?((t=Hs(n,e.mode,r,a)).return=e,t):((t=o(t,n)).return=e,t)}function h(e,t,n){if("string"===typeof t&&""!==t||"number"===typeof t||"bigint"===typeof t)return(t=Ws(""+t,e.mode,n)).return=e,t;if("object"===typeof t&&null!==t){switch(t.$$typeof){case c:return yo(n=Fs(t.type,t.key,t.props,null,e.mode,n),t),n.return=e,n;case u:return(t=Vs(t,e.mode,n)).return=e,t;case x:return h(e,t=(0,t._init)(t._payload),n)}if(U(t)||E(t))return(t=Hs(t,e.mode,n,null)).return=e,t;if("function"===typeof t.then)return h(e,vo(t),n);if(t.$$typeof===g)return h(e,Ci(e,t),n);bo(e,t)}return null}function v(e,t,n,r){var o=null!==t?t.key:null;if("string"===typeof n&&""!==n||"number"===typeof n||"bigint"===typeof n)return null!==o?null:s(e,t,""+n,r);if("object"===typeof n&&null!==n){switch(n.$$typeof){case c:return n.key===o?f(e,t,n,r):null;case u:return n.key===o?p(e,t,n,r):null;case x:return v(e,t,n=(o=n._init)(n._payload),r)}if(U(n)||E(n))return null!==o?null:m(e,t,n,r,null);if("function"===typeof n.then)return v(e,t,vo(n),r);if(n.$$typeof===g)return v(e,t,Ci(e,n),r);bo(e,n)}return null}function y(e,t,n,r,o){if("string"===typeof r&&""!==r||"number"===typeof r||"bigint"===typeof r)return s(t,e=e.get(n)||null,""+r,o);if("object"===typeof r&&null!==r){switch(r.$$typeof){case c:return f(t,e=e.get(null===r.key?n:r.key)||null,r,o);case u:return p(t,e=e.get(null===r.key?n:r.key)||null,r,o);case x:return y(e,t,n,r=(0,r._init)(r._payload),o)}if(U(r)||E(r))return m(t,e=e.get(n)||null,r,o,null);if("function"===typeof r.then)return y(e,t,n,vo(r),o);if(r.$$typeof===g)return y(e,t,n,Ci(t,r),o);bo(t,r)}return null}function b(s,f,p,m){if("object"===typeof p&&null!==p&&p.type===d&&null===p.key&&(p=p.props.children),"object"===typeof p&&null!==p){switch(p.$$typeof){case c:e:{for(var w=p.key;null!==f;){if(f.key===w){if((w=p.type)===d){if(7===f.tag){n(s,f.sibling),(m=o(f,p.props.children)).return=s,s=m;break e}}else if(f.elementType===w||"object"===typeof w&&null!==w&&w.$$typeof===x&&wo(w)===f.type){n(s,f.sibling),yo(m=o(f,p.props),p),m.return=s,s=m;break e}n(s,f);break}t(s,f),f=f.sibling}p.type===d?((m=Hs(p.props.children,s.mode,m,p.key)).return=s,s=m):(yo(m=Fs(p.type,p.key,p.props,null,s.mode,m),p),m.return=s,s=m)}return i(s);case u:e:{for(w=p.key;null!==f;){if(f.key===w){if(4===f.tag&&f.stateNode.containerInfo===p.containerInfo&&f.stateNode.implementation===p.implementation){n(s,f.sibling),(m=o(f,p.children||[])).return=s,s=m;break e}n(s,f);break}t(s,f),f=f.sibling}(m=Vs(p,s.mode,m)).return=s,s=m}return i(s);case x:return b(s,f,p=(w=p._init)(p._payload),m)}if(U(p))return function(o,l,i,s){for(var c=null,u=null,d=l,f=l=0,p=null;null!==d&&f<i.length;f++){d.index>f?(p=d,d=null):p=d.sibling;var m=v(o,d,i[f],s);if(null===m){null===d&&(d=p);break}e&&d&&null===m.alternate&&t(o,d),l=a(m,l,f),null===u?c=m:u.sibling=m,u=m,d=p}if(f===i.length)return n(o,d),Yr&&Br(o,f),c;if(null===d){for(;f<i.length;f++)null!==(d=h(o,i[f],s))&&(l=a(d,l,f),null===u?c=d:u.sibling=d,u=d);return Yr&&Br(o,f),c}for(d=r(d);f<i.length;f++)null!==(p=y(d,o,f,i[f],s))&&(e&&null!==p.alternate&&d.delete(null===p.key?f:p.key),l=a(p,l,f),null===u?c=p:u.sibling=p,u=p);return e&&d.forEach((function(e){return t(o,e)})),Yr&&Br(o,f),c}(s,f,p,m);if(E(p)){if("function"!==typeof(w=E(p)))throw Error(l(150));return function(o,i,s,c){if(null==s)throw Error(l(151));for(var u=null,d=null,f=i,p=i=0,m=null,g=s.next();null!==f&&!g.done;p++,g=s.next()){f.index>p?(m=f,f=null):m=f.sibling;var b=v(o,f,g.value,c);if(null===b){null===f&&(f=m);break}e&&f&&null===b.alternate&&t(o,f),i=a(b,i,p),null===d?u=b:d.sibling=b,d=b,f=m}if(g.done)return n(o,f),Yr&&Br(o,p),u;if(null===f){for(;!g.done;p++,g=s.next())null!==(g=h(o,g.value,c))&&(i=a(g,i,p),null===d?u=g:d.sibling=g,d=g);return Yr&&Br(o,p),u}for(f=r(f);!g.done;p++,g=s.next())null!==(g=y(f,o,p,g.value,c))&&(e&&null!==g.alternate&&f.delete(null===g.key?p:g.key),i=a(g,i,p),null===d?u=g:d.sibling=g,d=g);return e&&f.forEach((function(e){return t(o,e)})),Yr&&Br(o,p),u}(s,f,p=w.call(p),m)}if("function"===typeof p.then)return b(s,f,vo(p),m);if(p.$$typeof===g)return b(s,f,Ci(s,p),m);bo(s,p)}return"string"===typeof p&&""!==p||"number"===typeof p||"bigint"===typeof p?(p=""+p,null!==f&&6===f.tag?(n(s,f.sibling),(m=o(f,p)).return=s,s=m):(n(s,f),(m=Ws(p,s.mode,m)).return=s,s=m),i(s)):n(s,f)}return function(e,t,n,r){try{go=0;var o=b(e,t,n,r);return ho=null,o}catch(l){if(l===lo)throw l;var a=Ls(29,l,null,e.mode);return a.lanes=r,a.return=e,a}}}var ko=xo(!0),So=xo(!1),No=q(null),Eo=q(0);function Co(e,t){Q(Eo,e=cc),Q(No,t),cc=e|t.baseLanes}function jo(){Q(Eo,cc),Q(No,No.current)}function Po(){cc=Eo.current,K(No),K(Eo)}var _o=q(null),To=null;function Ro(e){var t=e.alternate;Q(Lo,1&Lo.current),Q(_o,e),null===To&&(null===t||null!==No.current||null!==t.memoizedState)&&(To=e)}function Ao(e){if(22===e.tag){if(Q(Lo,Lo.current),Q(_o,e),null===To){var t=e.alternate;null!==t&&null!==t.memoizedState&&(To=e)}}else zo()}function zo(){Q(Lo,Lo.current),Q(_o,_o.current)}function Oo(e){K(_o),To===e&&(To=null),K(Lo)}var Lo=q(0);function Mo(e){for(var t=e;null!==t;){if(13===t.tag){var n=t.memoizedState;if(null!==n&&(null===(n=n.dehydrated)||"$?"===n.data||"$!"===n.data))return t}else if(19===t.tag&&void 0!==t.memoizedProps.revealOrder){if(0!==(128&t.flags))return t}else if(null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Do="undefined"!==typeof AbortController?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(t,n){e.push(n)}};this.abort=function(){t.aborted=!0,e.forEach((function(e){return e()}))}},Io=r.unstable_scheduleCallback,Fo=r.unstable_NormalPriority,Ho={$$typeof:g,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Uo(){return{controller:new Do,data:new Map,refCount:0}}function Wo(e){e.refCount--,0===e.refCount&&Io(Fo,(function(){e.controller.abort()}))}var Vo=null,Bo=0,$o=0,qo=null;function Ko(){if(0===--Bo&&null!==Vo){null!==qo&&(qo.status="fulfilled");var e=Vo;Vo=null,$o=0,qo=null;for(var t=0;t<e.length;t++)(0,e[t])()}}var Qo=T.S;T.S=function(e,t){"object"===typeof t&&null!==t&&"function"===typeof t.then&&function(e,t){if(null===Vo){var n=Vo=[];Bo=0,$o=xu(),qo={status:"pending",value:void 0,then:function(e){n.push(e)}}}Bo++,t.then(Ko,Ko)}(0,t),null!==Qo&&Qo(e,t)};var Go=q(null);function Yo(){var e=Go.current;return null!==e?e:tc.pooledCache}function Xo(e,t){Q(Go,null===t?Go.current:t.pool)}function Zo(){var e=Yo();return null===e?null:{parent:Ho._currentValue,pool:e}}var Jo=0,ea=null,ta=null,na=null,ra=!1,oa=!1,aa=!1,la=0,ia=0,sa=null,ca=0;function ua(){throw Error(l(321))}function da(e,t){if(null===t)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Qn(e[n],t[n]))return!1;return!0}function fa(e,t,n,r,o,a){return Jo=a,ea=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,T.H=null===e||null===e.memoizedState?Cl:jl,aa=!1,a=n(r,o),aa=!1,oa&&(a=ma(t,n,r,o)),pa(e),a}function pa(e){T.H=El;var t=null!==ta&&null!==ta.next;if(Jo=0,na=ta=ea=null,ra=!1,ia=0,sa=null,t)throw Error(l(300));null===e||Bl||null!==(e=e.dependencies)&&Si(e)&&(Bl=!0)}function ma(e,t,n,r){ea=e;var o=0;do{if(oa&&(sa=null),ia=0,oa=!1,25<=o)throw Error(l(301));if(o+=1,na=ta=null,null!=e.updateQueue){var a=e.updateQueue;a.lastEffect=null,a.events=null,a.stores=null,null!=a.memoCache&&(a.memoCache.index=0)}T.H=Pl,a=t(n,r)}while(oa);return a}function ha(){var e=T.H,t=e.useState()[0];return t="function"===typeof t.then?xa(t):t,e=e.useState()[0],(null!==ta?ta.memoizedState:null)!==e&&(ea.flags|=1024),t}function ga(){var e=0!==la;return la=0,e}function va(e,t,n){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~n}function ya(e){if(ra){for(e=e.memoizedState;null!==e;){var t=e.queue;null!==t&&(t.pending=null),e=e.next}ra=!1}Jo=0,na=ta=ea=null,oa=!1,ia=la=0,sa=null}function ba(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return null===na?ea.memoizedState=na=e:na=na.next=e,na}function wa(){if(null===ta){var e=ea.alternate;e=null!==e?e.memoizedState:null}else e=ta.next;var t=null===na?ea.memoizedState:na.next;if(null!==t)na=t,ta=e;else{if(null===e){if(null===ea.alternate)throw Error(l(467));throw Error(l(310))}e={memoizedState:(ta=e).memoizedState,baseState:ta.baseState,baseQueue:ta.baseQueue,queue:ta.queue,next:null},null===na?ea.memoizedState=na=e:na=na.next=e}return na}function xa(e){var t=ia;return ia+=1,null===sa&&(sa=[]),e=fo(sa,e,t),t=ea,null===(null===na?t.memoizedState:na.next)&&(t=t.alternate,T.H=null===t||null===t.memoizedState?Cl:jl),e}function ka(e){if(null!==e&&"object"===typeof e){if("function"===typeof e.then)return xa(e);if(e.$$typeof===g)return Ei(e)}throw Error(l(438,String(e)))}function Sa(e){var t=null,n=ea.updateQueue;if(null!==n&&(t=n.memoCache),null==t){var r=ea.alternate;null!==r&&(null!==(r=r.updateQueue)&&(null!=(r=r.memoCache)&&(t={data:r.data.map((function(e){return e.slice()})),index:0})))}if(null==t&&(t={data:[],index:0}),null===n&&(n={lastEffect:null,events:null,stores:null,memoCache:null},ea.updateQueue=n),n.memoCache=t,void 0===(n=t.data[t.index]))for(n=t.data[t.index]=Array(e),r=0;r<e;r++)n[r]=S;return t.index++,n}function Na(e,t){return"function"===typeof t?t(e):t}function Ea(e){return Ca(wa(),ta,e)}function Ca(e,t,n){var r=e.queue;if(null===r)throw Error(l(311));r.lastRenderedReducer=n;var o=e.baseQueue,a=r.pending;if(null!==a){if(null!==o){var i=o.next;o.next=a.next,a.next=i}t.baseQueue=o=a,r.pending=null}if(a=e.baseState,null===o)e.memoizedState=a;else{var s=i=null,c=null,u=t=o.next,d=!1;do{var f=-536870913&u.lane;if(f!==u.lane?(rc&f)===f:(Jo&f)===f){var p=u.revertLane;if(0===p)null!==c&&(c=c.next={lane:0,revertLane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),f===$o&&(d=!0);else{if((Jo&p)===p){u=u.next,p===$o&&(d=!0);continue}f={lane:0,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(s=c=f,i=a):c=c.next=f,ea.lanes|=p,dc|=p}f=u.action,aa&&n(a,f),a=u.hasEagerState?u.eagerState:n(a,f)}else p={lane:f,revertLane:u.revertLane,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null},null===c?(s=c=p,i=a):c=c.next=p,ea.lanes|=f,dc|=f;u=u.next}while(null!==u&&u!==t);if(null===c?i=a:c.next=s,!Qn(a,e.memoizedState)&&(Bl=!0,d&&null!==(n=qo)))throw n;e.memoizedState=a,e.baseState=i,e.baseQueue=c,r.lastRenderedState=a}return null===o&&(r.lanes=0),[e.memoizedState,r.dispatch]}function ja(e){var t=wa(),n=t.queue;if(null===n)throw Error(l(311));n.lastRenderedReducer=e;var r=n.dispatch,o=n.pending,a=t.memoizedState;if(null!==o){n.pending=null;var i=o=o.next;do{a=e(a,i.action),i=i.next}while(i!==o);Qn(a,t.memoizedState)||(Bl=!0),t.memoizedState=a,null===t.baseQueue&&(t.baseState=a),n.lastRenderedState=a}return[a,r]}function Pa(e,t,n){var r=ea,o=wa(),a=Yr;if(a){if(void 0===n)throw Error(l(407));n=n()}else n=t();var i=!Qn((ta||o).memoizedState,n);if(i&&(o.memoizedState=n,Bl=!0),o=o.queue,Ja(Ra.bind(null,r,o,e),[e]),o.getSnapshot!==t||i||null!==na&&1&na.memoizedState.tag){if(r.flags|=2048,Qa(9,Ta.bind(null,r,o,n,t),{destroy:void 0},null),null===tc)throw Error(l(349));a||0!==(60&Jo)||_a(r,t,n)}return n}function _a(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},null===(t=ea.updateQueue)?(t={lastEffect:null,events:null,stores:null,memoCache:null},ea.updateQueue=t,t.stores=[e]):null===(n=t.stores)?t.stores=[e]:n.push(e)}function Ta(e,t,n,r){t.value=n,t.getSnapshot=r,Aa(t)&&za(e)}function Ra(e,t,n){return n((function(){Aa(t)&&za(e)}))}function Aa(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Qn(e,n)}catch(r){return!0}}function za(e){var t=_r(e,2);null!==t&&Ac(t,e,2)}function Oa(e){var t=ba();if("function"===typeof e){var n=e;if(e=n(),aa){be(!0);try{n()}finally{be(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Na,lastRenderedState:e},t}function La(e,t,n,r){return e.baseState=n,Ca(e,ta,"function"===typeof r?r:Na)}function Ma(e,t,n,r,o){if(kl(e))throw Error(l(485));if(null!==(e=t.action)){var a={payload:o,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(e){a.listeners.push(e)}};null!==T.T?n(!0):a.isTransition=!1,r(a),null===(n=t.pending)?(a.next=t.pending=a,Da(t,a)):(a.next=n.next,t.pending=n.next=a)}}function Da(e,t){var n=t.action,r=t.payload,o=e.state;if(t.isTransition){var a=T.T,l={};T.T=l;try{var i=n(o,r),s=T.S;null!==s&&s(l,i),Ia(e,t,i)}catch(c){Ha(e,t,c)}finally{T.T=a}}else try{Ia(e,t,a=n(o,r))}catch(u){Ha(e,t,u)}}function Ia(e,t,n){null!==n&&"object"===typeof n&&"function"===typeof n.then?n.then((function(n){Fa(e,t,n)}),(function(n){return Ha(e,t,n)})):Fa(e,t,n)}function Fa(e,t,n){t.status="fulfilled",t.value=n,Ua(t),e.state=n,null!==(t=e.pending)&&((n=t.next)===t?e.pending=null:(n=n.next,t.next=n,Da(e,n)))}function Ha(e,t,n){var r=e.pending;if(e.pending=null,null!==r){r=r.next;do{t.status="rejected",t.reason=n,Ua(t),t=t.next}while(t!==r)}e.action=null}function Ua(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Wa(e,t){return t}function Va(e,t){if(Yr){var n=tc.formState;if(null!==n){e:{var r=ea;if(Yr){if(Gr){t:{for(var o=Gr,a=Zr;8!==o.nodeType;){if(!a){o=null;break t}if(null===(o=id(o.nextSibling))){o=null;break t}}o="F!"===(a=o.data)||"F"===a?o:null}if(o){Gr=id(o.nextSibling),r="F!"===o.data;break e}}eo(r)}r=!1}r&&(t=n[0])}}return(n=ba()).memoizedState=n.baseState=t,r={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Wa,lastRenderedState:t},n.queue=r,n=bl.bind(null,ea,r),r.dispatch=n,r=Oa(!1),a=xl.bind(null,ea,!1,r.queue),o={state:t,dispatch:null,action:e,pending:null},(r=ba()).queue=o,n=Ma.bind(null,ea,o,a,n),o.dispatch=n,r.memoizedState=e,[t,n,!1]}function Ba(e){return $a(wa(),ta,e)}function $a(e,t,n){t=Ca(e,t,Wa)[0],e=Ea(Na)[0],t="object"===typeof t&&null!==t&&"function"===typeof t.then?xa(t):t;var r=wa(),o=r.queue,a=o.dispatch;return n!==r.memoizedState&&(ea.flags|=2048,Qa(9,qa.bind(null,o,n),{destroy:void 0},null)),[t,a,e]}function qa(e,t){e.action=t}function Ka(e){var t=wa(),n=ta;if(null!==n)return $a(t,n,e);wa(),t=t.memoizedState;var r=(n=wa()).queue.dispatch;return n.memoizedState=e,[t,r,!1]}function Qa(e,t,n,r){return e={tag:e,create:t,inst:n,deps:r,next:null},null===(t=ea.updateQueue)&&(t={lastEffect:null,events:null,stores:null,memoCache:null},ea.updateQueue=t),null===(n=t.lastEffect)?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e),e}function Ga(){return wa().memoizedState}function Ya(e,t,n,r){var o=ba();ea.flags|=e,o.memoizedState=Qa(1|t,n,{destroy:void 0},void 0===r?null:r)}function Xa(e,t,n,r){var o=wa();r=void 0===r?null:r;var a=o.memoizedState.inst;null!==ta&&null!==r&&da(r,ta.memoizedState.deps)?o.memoizedState=Qa(t,n,a,r):(ea.flags|=e,o.memoizedState=Qa(1|t,n,a,r))}function Za(e,t){Ya(8390656,8,e,t)}function Ja(e,t){Xa(2048,8,e,t)}function el(e,t){return Xa(4,2,e,t)}function tl(e,t){return Xa(4,4,e,t)}function nl(e,t){if("function"===typeof t){e=e();var n=t(e);return function(){"function"===typeof n?n():t(null)}}if(null!==t&&void 0!==t)return e=e(),t.current=e,function(){t.current=null}}function rl(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,Xa(4,4,nl.bind(null,t,e),n)}function ol(){}function al(e,t){var n=wa();t=void 0===t?null:t;var r=n.memoizedState;return null!==t&&da(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function ll(e,t){var n=wa();t=void 0===t?null:t;var r=n.memoizedState;if(null!==t&&da(t,r[1]))return r[0];if(r=e(),aa){be(!0);try{e()}finally{be(!1)}}return n.memoizedState=[r,t],r}function il(e,t,n){return void 0===n||0!==(1073741824&Jo)?e.memoizedState=t:(e.memoizedState=n,e=Rc(),ea.lanes|=e,dc|=e,n)}function sl(e,t,n,r){return Qn(n,t)?n:null!==No.current?(e=il(e,n,r),Qn(e,t)||(Bl=!0),e):0===(42&Jo)?(Bl=!0,e.memoizedState=n):(e=Rc(),ea.lanes|=e,dc|=e,t)}function cl(e,t,n,r,o){var a=W.p;W.p=0!==a&&8>a?a:8;var l=T.T,i={};T.T=i,xl(e,!1,t,n);try{var s=o(),c=T.S;if(null!==c&&c(i,s),null!==s&&"object"===typeof s&&"function"===typeof s.then){var u=function(e,t){var n=[],r={status:"pending",value:null,reason:null,then:function(e){n.push(e)}};return e.then((function(){r.status="fulfilled",r.value=t;for(var e=0;e<n.length;e++)(0,n[e])(t)}),(function(e){for(r.status="rejected",r.reason=e,e=0;e<n.length;e++)(0,n[e])(void 0)})),r}(s,r);wl(e,t,u,Tc())}else wl(e,t,r,Tc())}catch(d){wl(e,t,{then:function(){},status:"rejected",reason:d},Tc())}finally{W.p=a,T.T=l}}function ul(){}function dl(e,t,n,r){if(5!==e.tag)throw Error(l(476));var o=fl(e).queue;cl(e,o,t,V,null===n?ul:function(){return pl(e),n(r)})}function fl(e){var t=e.memoizedState;if(null!==t)return t;var n={};return(t={memoizedState:V,baseState:V,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Na,lastRenderedState:V},next:null}).next={memoizedState:n,baseState:n,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Na,lastRenderedState:n},next:null},e.memoizedState=t,null!==(e=e.alternate)&&(e.memoizedState=t),t}function pl(e){wl(e,fl(e).next.queue,{},Tc())}function ml(){return Ei(Md)}function hl(){return wa().memoizedState}function gl(){return wa().memoizedState}function vl(e){for(var t=e.return;null!==t;){switch(t.tag){case 24:case 3:var n=Tc(),r=Ai(t,e=Ri(n),n);return null!==r&&(Ac(r,t,n),zi(r,t,n)),t={cache:Uo()},void(e.payload=t)}t=t.return}}function yl(e,t,n){var r=Tc();n={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null},kl(e)?Sl(t,n):null!==(n=Pr(e,t,n,r))&&(Ac(n,e,r),Nl(n,t,r))}function bl(e,t,n){wl(e,t,n,Tc())}function wl(e,t,n,r){var o={lane:r,revertLane:0,action:n,hasEagerState:!1,eagerState:null,next:null};if(kl(e))Sl(t,o);else{var a=e.alternate;if(0===e.lanes&&(null===a||0===a.lanes)&&null!==(a=t.lastRenderedReducer))try{var l=t.lastRenderedState,i=a(l,n);if(o.hasEagerState=!0,o.eagerState=i,Qn(i,l))return jr(e,t,o,0),null===tc&&Cr(),!1}catch(s){}if(null!==(n=Pr(e,t,o,r)))return Ac(n,e,r),Nl(n,t,r),!0}return!1}function xl(e,t,n,r){if(r={lane:2,revertLane:xu(),action:r,hasEagerState:!1,eagerState:null,next:null},kl(e)){if(t)throw Error(l(479))}else null!==(t=Pr(e,n,r,2))&&Ac(t,e,2)}function kl(e){var t=e.alternate;return e===ea||null!==t&&t===ea}function Sl(e,t){oa=ra=!0;var n=e.pending;null===n?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function Nl(e,t,n){if(0!==(4194176&n)){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Oe(e,n)}}var El={readContext:Ei,use:ka,useCallback:ua,useContext:ua,useEffect:ua,useImperativeHandle:ua,useLayoutEffect:ua,useInsertionEffect:ua,useMemo:ua,useReducer:ua,useRef:ua,useState:ua,useDebugValue:ua,useDeferredValue:ua,useTransition:ua,useSyncExternalStore:ua,useId:ua};El.useCacheRefresh=ua,El.useMemoCache=ua,El.useHostTransitionStatus=ua,El.useFormState=ua,El.useActionState=ua,El.useOptimistic=ua;var Cl={readContext:Ei,use:ka,useCallback:function(e,t){return ba().memoizedState=[e,void 0===t?null:t],e},useContext:Ei,useEffect:Za,useImperativeHandle:function(e,t,n){n=null!==n&&void 0!==n?n.concat([e]):null,Ya(4194308,4,nl.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Ya(4194308,4,e,t)},useInsertionEffect:function(e,t){Ya(4,2,e,t)},useMemo:function(e,t){var n=ba();t=void 0===t?null:t;var r=e();if(aa){be(!0);try{e()}finally{be(!1)}}return n.memoizedState=[r,t],r},useReducer:function(e,t,n){var r=ba();if(void 0!==n){var o=n(t);if(aa){be(!0);try{n(t)}finally{be(!1)}}}else o=t;return r.memoizedState=r.baseState=o,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:o},r.queue=e,e=e.dispatch=yl.bind(null,ea,e),[r.memoizedState,e]},useRef:function(e){return e={current:e},ba().memoizedState=e},useState:function(e){var t=(e=Oa(e)).queue,n=bl.bind(null,ea,t);return t.dispatch=n,[e.memoizedState,n]},useDebugValue:ol,useDeferredValue:function(e,t){return il(ba(),e,t)},useTransition:function(){var e=Oa(!1);return e=cl.bind(null,ea,e.queue,!0,!1),ba().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,n){var r=ea,o=ba();if(Yr){if(void 0===n)throw Error(l(407));n=n()}else{if(n=t(),null===tc)throw Error(l(349));0!==(60&rc)||_a(r,t,n)}o.memoizedState=n;var a={value:n,getSnapshot:t};return o.queue=a,Za(Ra.bind(null,r,a,e),[e]),r.flags|=2048,Qa(9,Ta.bind(null,r,a,n,t),{destroy:void 0},null),n},useId:function(){var e=ba(),t=tc.identifierPrefix;if(Yr){var n=Vr;t=":"+t+"R"+(n=(Wr&~(1<<32-we(Wr)-1)).toString(32)+n),0<(n=la++)&&(t+="H"+n.toString(32)),t+=":"}else t=":"+t+"r"+(n=ca++).toString(32)+":";return e.memoizedState=t},useCacheRefresh:function(){return ba().memoizedState=vl.bind(null,ea)}};Cl.useMemoCache=Sa,Cl.useHostTransitionStatus=ml,Cl.useFormState=Va,Cl.useActionState=Va,Cl.useOptimistic=function(e){var t=ba();t.memoizedState=t.baseState=e;var n={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=n,t=xl.bind(null,ea,!0,n),n.dispatch=t,[e,t]};var jl={readContext:Ei,use:ka,useCallback:al,useContext:Ei,useEffect:Ja,useImperativeHandle:rl,useInsertionEffect:el,useLayoutEffect:tl,useMemo:ll,useReducer:Ea,useRef:Ga,useState:function(){return Ea(Na)},useDebugValue:ol,useDeferredValue:function(e,t){return sl(wa(),ta.memoizedState,e,t)},useTransition:function(){var e=Ea(Na)[0],t=wa().memoizedState;return["boolean"===typeof e?e:xa(e),t]},useSyncExternalStore:Pa,useId:hl};jl.useCacheRefresh=gl,jl.useMemoCache=Sa,jl.useHostTransitionStatus=ml,jl.useFormState=Ba,jl.useActionState=Ba,jl.useOptimistic=function(e,t){return La(wa(),0,e,t)};var Pl={readContext:Ei,use:ka,useCallback:al,useContext:Ei,useEffect:Ja,useImperativeHandle:rl,useInsertionEffect:el,useLayoutEffect:tl,useMemo:ll,useReducer:ja,useRef:Ga,useState:function(){return ja(Na)},useDebugValue:ol,useDeferredValue:function(e,t){var n=wa();return null===ta?il(n,e,t):sl(n,ta.memoizedState,e,t)},useTransition:function(){var e=ja(Na)[0],t=wa().memoizedState;return["boolean"===typeof e?e:xa(e),t]},useSyncExternalStore:Pa,useId:hl};function _l(e,t,n,r){n=null===(n=n(r,t=e.memoizedState))||void 0===n?t:R({},t,n),e.memoizedState=n,0===e.lanes&&(e.updateQueue.baseState=n)}Pl.useCacheRefresh=gl,Pl.useMemoCache=Sa,Pl.useHostTransitionStatus=ml,Pl.useFormState=Ka,Pl.useActionState=Ka,Pl.useOptimistic=function(e,t){var n=wa();return null!==ta?La(n,0,e,t):(n.baseState=e,[e,n.queue.dispatch])};var Tl={isMounted:function(e){return!!(e=e._reactInternals)&&D(e)===e},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Tc(),o=Ri(r);o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Ai(e,o,r))&&(Ac(t,e,r),zi(t,e,r))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Tc(),o=Ri(r);o.tag=1,o.payload=t,void 0!==n&&null!==n&&(o.callback=n),null!==(t=Ai(e,o,r))&&(Ac(t,e,r),zi(t,e,r))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Tc(),r=Ri(n);r.tag=2,void 0!==t&&null!==t&&(r.callback=t),null!==(t=Ai(e,r,n))&&(Ac(t,e,n),zi(t,e,n))}};function Rl(e,t,n,r,o,a,l){return"function"===typeof(e=e.stateNode).shouldComponentUpdate?e.shouldComponentUpdate(r,a,l):!t.prototype||!t.prototype.isPureReactComponent||(!Gn(n,r)||!Gn(o,a))}function Al(e,t,n,r){e=t.state,"function"===typeof t.componentWillReceiveProps&&t.componentWillReceiveProps(n,r),"function"===typeof t.UNSAFE_componentWillReceiveProps&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Tl.enqueueReplaceState(t,t.state,null)}function zl(e,t){var n=t;if("ref"in t)for(var r in n={},t)"ref"!==r&&(n[r]=t[r]);if(e=e.defaultProps)for(var o in n===t&&(n=R({},n)),e)void 0===n[o]&&(n[o]=e[o]);return n}var Ol="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function Ll(e){Ol(e)}function Ml(e){console.error(e)}function Dl(e){Ol(e)}function Il(e,t){try{(0,e.onUncaughtError)(t.value,{componentStack:t.stack})}catch(n){setTimeout((function(){throw n}))}}function Fl(e,t,n){try{(0,e.onCaughtError)(n.value,{componentStack:n.stack,errorBoundary:1===t.tag?t.stateNode:null})}catch(r){setTimeout((function(){throw r}))}}function Hl(e,t,n){return(n=Ri(n)).tag=3,n.payload={element:null},n.callback=function(){Il(e,t)},n}function Ul(e){return(e=Ri(e)).tag=3,e}function Wl(e,t,n,r){var o=n.type.getDerivedStateFromError;if("function"===typeof o){var a=r.value;e.payload=function(){return o(a)},e.callback=function(){Fl(t,n,r)}}var l=n.stateNode;null!==l&&"function"===typeof l.componentDidCatch&&(e.callback=function(){Fl(t,n,r),"function"!==typeof o&&(null===kc?kc=new Set([this]):kc.add(this));var e=r.stack;this.componentDidCatch(r.value,{componentStack:null!==e?e:""})})}var Vl=Error(l(461)),Bl=!1;function $l(e,t,n,r){t.child=null===e?So(t,null,n,r):ko(t,e.child,n,r)}function ql(e,t,n,r,o){n=n.render;var a=t.ref;if("ref"in r){var l={};for(var i in r)"ref"!==i&&(l[i]=r[i])}else l=r;return Ni(t),r=fa(e,t,n,l,a,o),i=ga(),null===e||Bl?(Yr&&i&&qr(t),t.flags|=1,$l(e,t,r,o),t.child):(va(e,t,o),fi(e,t,o))}function Kl(e,t,n,r,o){if(null===e){var a=n.type;return"function"!==typeof a||Ms(a)||void 0!==a.defaultProps||null!==n.compare?((e=Fs(n.type,null,r,t,t.mode,o)).ref=t.ref,e.return=t,t.child=e):(t.tag=15,t.type=a,Ql(e,t,a,r,o))}if(a=e.child,!pi(e,o)){var l=a.memoizedProps;if((n=null!==(n=n.compare)?n:Gn)(l,r)&&e.ref===t.ref)return fi(e,t,o)}return t.flags|=1,(e=Ds(a,r)).ref=t.ref,e.return=t,t.child=e}function Ql(e,t,n,r,o){if(null!==e){var a=e.memoizedProps;if(Gn(a,r)&&e.ref===t.ref){if(Bl=!1,t.pendingProps=r=a,!pi(e,o))return t.lanes=e.lanes,fi(e,t,o);0!==(131072&e.flags)&&(Bl=!0)}}return Zl(e,t,n,r,o)}function Gl(e,t,n){var r=t.pendingProps,o=r.children,a=0!==(2&t.stateNode._pendingVisibility),l=null!==e?e.memoizedState:null;if(Xl(e,t),"hidden"===r.mode||a){if(0!==(128&t.flags)){if(r=null!==l?l.baseLanes|n:n,null!==e){for(o=t.child=e.child,a=0;null!==o;)a=a|o.lanes|o.childLanes,o=o.sibling;t.childLanes=a&~r}else t.childLanes=0,t.child=null;return Yl(e,t,r,n)}if(0===(536870912&n))return t.lanes=t.childLanes=536870912,Yl(e,t,null!==l?l.baseLanes|n:n,n);t.memoizedState={baseLanes:0,cachePool:null},null!==e&&Xo(0,null!==l?l.cachePool:null),null!==l?Co(t,l):jo(),Ao(t)}else null!==l?(Xo(0,l.cachePool),Co(t,l),zo(),t.memoizedState=null):(null!==e&&Xo(0,null),jo(),zo());return $l(e,t,o,n),t.child}function Yl(e,t,n,r){var o=Yo();return o=null===o?null:{parent:Ho._currentValue,pool:o},t.memoizedState={baseLanes:n,cachePool:o},null!==e&&Xo(0,null),jo(),Ao(t),null!==e&&ki(e,t,r,!0),null}function Xl(e,t){var n=t.ref;if(null===n)null!==e&&null!==e.ref&&(t.flags|=2097664);else{if("function"!==typeof n&&"object"!==typeof n)throw Error(l(284));null!==e&&e.ref===n||(t.flags|=2097664)}}function Zl(e,t,n,r,o){return Ni(t),n=fa(e,t,n,r,void 0,o),r=ga(),null===e||Bl?(Yr&&r&&qr(t),t.flags|=1,$l(e,t,n,o),t.child):(va(e,t,o),fi(e,t,o))}function Jl(e,t,n,r,o,a){return Ni(t),t.updateQueue=null,n=ma(t,r,n,o),pa(e),r=ga(),null===e||Bl?(Yr&&r&&qr(t),t.flags|=1,$l(e,t,n,a),t.child):(va(e,t,a),fi(e,t,a))}function ei(e,t,n,r,o){if(Ni(t),null===t.stateNode){var a=Ar,l=n.contextType;"object"===typeof l&&null!==l&&(a=Ei(l)),a=new n(r,a),t.memoizedState=null!==a.state&&void 0!==a.state?a.state:null,a.updater=Tl,t.stateNode=a,a._reactInternals=t,(a=t.stateNode).props=r,a.state=t.memoizedState,a.refs={},_i(t),l=n.contextType,a.context="object"===typeof l&&null!==l?Ei(l):Ar,a.state=t.memoizedState,"function"===typeof(l=n.getDerivedStateFromProps)&&(_l(t,n,l,r),a.state=t.memoizedState),"function"===typeof n.getDerivedStateFromProps||"function"===typeof a.getSnapshotBeforeUpdate||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||(l=a.state,"function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount(),l!==a.state&&Tl.enqueueReplaceState(a,a.state,null),Di(t,r,a,o),Mi(),a.state=t.memoizedState),"function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!0}else if(null===e){a=t.stateNode;var i=t.memoizedProps,s=zl(n,i);a.props=s;var c=a.context,u=n.contextType;l=Ar,"object"===typeof u&&null!==u&&(l=Ei(u));var d=n.getDerivedStateFromProps;u="function"===typeof d||"function"===typeof a.getSnapshotBeforeUpdate,i=t.pendingProps!==i,u||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(i||c!==l)&&Al(t,a,r,l),Pi=!1;var f=t.memoizedState;a.state=f,Di(t,r,a,o),Mi(),c=t.memoizedState,i||f!==c||Pi?("function"===typeof d&&(_l(t,n,d,r),c=t.memoizedState),(s=Pi||Rl(t,n,s,r,f,c,l))?(u||"function"!==typeof a.UNSAFE_componentWillMount&&"function"!==typeof a.componentWillMount||("function"===typeof a.componentWillMount&&a.componentWillMount(),"function"===typeof a.UNSAFE_componentWillMount&&a.UNSAFE_componentWillMount()),"function"===typeof a.componentDidMount&&(t.flags|=4194308)):("function"===typeof a.componentDidMount&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=c),a.props=r,a.state=c,a.context=l,r=s):("function"===typeof a.componentDidMount&&(t.flags|=4194308),r=!1)}else{a=t.stateNode,Ti(e,t),u=zl(n,l=t.memoizedProps),a.props=u,d=t.pendingProps,f=a.context,c=n.contextType,s=Ar,"object"===typeof c&&null!==c&&(s=Ei(c)),(c="function"===typeof(i=n.getDerivedStateFromProps)||"function"===typeof a.getSnapshotBeforeUpdate)||"function"!==typeof a.UNSAFE_componentWillReceiveProps&&"function"!==typeof a.componentWillReceiveProps||(l!==d||f!==s)&&Al(t,a,r,s),Pi=!1,f=t.memoizedState,a.state=f,Di(t,r,a,o),Mi();var p=t.memoizedState;l!==d||f!==p||Pi||null!==e&&null!==e.dependencies&&Si(e.dependencies)?("function"===typeof i&&(_l(t,n,i,r),p=t.memoizedState),(u=Pi||Rl(t,n,u,r,f,p,s)||null!==e&&null!==e.dependencies&&Si(e.dependencies))?(c||"function"!==typeof a.UNSAFE_componentWillUpdate&&"function"!==typeof a.componentWillUpdate||("function"===typeof a.componentWillUpdate&&a.componentWillUpdate(r,p,s),"function"===typeof a.UNSAFE_componentWillUpdate&&a.UNSAFE_componentWillUpdate(r,p,s)),"function"===typeof a.componentDidUpdate&&(t.flags|=4),"function"===typeof a.getSnapshotBeforeUpdate&&(t.flags|=1024)):("function"!==typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=p),a.props=r,a.state=p,a.context=s,r=u):("function"!==typeof a.componentDidUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=4),"function"!==typeof a.getSnapshotBeforeUpdate||l===e.memoizedProps&&f===e.memoizedState||(t.flags|=1024),r=!1)}return a=r,Xl(e,t),r=0!==(128&t.flags),a||r?(a=t.stateNode,n=r&&"function"!==typeof n.getDerivedStateFromError?null:a.render(),t.flags|=1,null!==e&&r?(t.child=ko(t,e.child,null,o),t.child=ko(t,null,n,o)):$l(e,t,n,o),t.memoizedState=a.state,e=t.child):e=fi(e,t,o),e}function ti(e,t,n,r){return oo(),t.flags|=256,$l(e,t,n,r),t.child}var ni={dehydrated:null,treeContext:null,retryLane:0};function ri(e){return{baseLanes:e,cachePool:Zo()}}function oi(e,t,n){return e=null!==e?e.childLanes&~n:0,t&&(e|=mc),e}function ai(e,t,n){var r,o=t.pendingProps,a=!1,i=0!==(128&t.flags);if((r=i)||(r=(null===e||null!==e.memoizedState)&&0!==(2&Lo.current)),r&&(a=!0,t.flags&=-129),r=0!==(32&t.flags),t.flags&=-33,null===e){if(Yr){if(a?Ro(t):zo(),Yr){var s,c=Gr;if(s=c){e:{for(s=c,c=Zr;8!==s.nodeType;){if(!c){c=null;break e}if(null===(s=id(s.nextSibling))){c=null;break e}}c=s}null!==c?(t.memoizedState={dehydrated:c,treeContext:null!==Ur?{id:Wr,overflow:Vr}:null,retryLane:536870912},(s=Ls(18,null,null,0)).stateNode=c,s.return=t,t.child=s,Qr=t,Gr=null,s=!0):s=!1}s||eo(t)}if(null!==(c=t.memoizedState)&&null!==(c=c.dehydrated))return"$!"===c.data?t.lanes=16:t.lanes=536870912,null;Oo(t)}return c=o.children,o=o.fallback,a?(zo(),c=ii({mode:"hidden",children:c},a=t.mode),o=Hs(o,a,n,null),c.return=t,o.return=t,c.sibling=o,t.child=c,(a=t.child).memoizedState=ri(n),a.childLanes=oi(e,r,n),t.memoizedState=ni,o):(Ro(t),li(t,c))}if(null!==(s=e.memoizedState)&&null!==(c=s.dehydrated)){if(i)256&t.flags?(Ro(t),t.flags&=-257,t=si(e,t,n)):null!==t.memoizedState?(zo(),t.child=e.child,t.flags|=128,t=null):(zo(),a=o.fallback,c=t.mode,o=ii({mode:"visible",children:o.children},c),(a=Hs(a,c,n,null)).flags|=2,o.return=t,a.return=t,o.sibling=a,t.child=o,ko(t,e.child,null,n),(o=t.child).memoizedState=ri(n),o.childLanes=oi(e,r,n),t.memoizedState=ni,t=a);else if(Ro(t),"$!"===c.data){if(r=c.nextSibling&&c.nextSibling.dataset)var u=r.dgst;r=u,(o=Error(l(419))).stack="",o.digest=r,ao({value:o,source:null,stack:null}),t=si(e,t,n)}else if(Bl||ki(e,t,n,!1),r=0!==(n&e.childLanes),Bl||r){if(null!==(r=tc)){if(0!==(42&(o=n&-n)))o=1;else switch(o){case 2:o=1;break;case 8:o=4;break;case 32:o=16;break;case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:o=64;break;case 268435456:o=134217728;break;default:o=0}if(0!==(o=0!==(o&(r.suspendedLanes|n))?0:o)&&o!==s.retryLane)throw s.retryLane=o,_r(e,o),Ac(r,e,o),Vl}"$?"===c.data||Bc(),t=si(e,t,n)}else"$?"===c.data?(t.flags|=128,t.child=e.child,t=iu.bind(null,e),c._reactRetry=t,t=null):(e=s.treeContext,Gr=id(c.nextSibling),Qr=t,Yr=!0,Xr=null,Zr=!1,null!==e&&(Fr[Hr++]=Wr,Fr[Hr++]=Vr,Fr[Hr++]=Ur,Wr=e.id,Vr=e.overflow,Ur=t),(t=li(t,o.children)).flags|=4096);return t}return a?(zo(),a=o.fallback,c=t.mode,u=(s=e.child).sibling,(o=Ds(s,{mode:"hidden",children:o.children})).subtreeFlags=31457280&s.subtreeFlags,null!==u?a=Ds(u,a):(a=Hs(a,c,n,null)).flags|=2,a.return=t,o.return=t,o.sibling=a,t.child=o,o=a,a=t.child,null===(c=e.child.memoizedState)?c=ri(n):(null!==(s=c.cachePool)?(u=Ho._currentValue,s=s.parent!==u?{parent:u,pool:u}:s):s=Zo(),c={baseLanes:c.baseLanes|n,cachePool:s}),a.memoizedState=c,a.childLanes=oi(e,r,n),t.memoizedState=ni,o):(Ro(t),e=(n=e.child).sibling,(n=Ds(n,{mode:"visible",children:o.children})).return=t,n.sibling=null,null!==e&&(null===(r=t.deletions)?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=n,t.memoizedState=null,n)}function li(e,t){return(t=ii({mode:"visible",children:t},e.mode)).return=e,e.child=t}function ii(e,t){return Us(e,t,0,null)}function si(e,t,n){return ko(t,e.child,null,n),(e=li(t,t.pendingProps.children)).flags|=2,t.memoizedState=null,e}function ci(e,t,n){e.lanes|=t;var r=e.alternate;null!==r&&(r.lanes|=t),wi(e.return,t,n)}function ui(e,t,n,r,o){var a=e.memoizedState;null===a?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:o}:(a.isBackwards=t,a.rendering=null,a.renderingStartTime=0,a.last=r,a.tail=n,a.tailMode=o)}function di(e,t,n){var r=t.pendingProps,o=r.revealOrder,a=r.tail;if($l(e,t,r.children,n),0!==(2&(r=Lo.current)))r=1&r|2,t.flags|=128;else{if(null!==e&&0!==(128&e.flags))e:for(e=t.child;null!==e;){if(13===e.tag)null!==e.memoizedState&&ci(e,n,t);else if(19===e.tag)ci(e,n,t);else if(null!==e.child){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;null===e.sibling;){if(null===e.return||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}switch(Q(Lo,r),o){case"forwards":for(n=t.child,o=null;null!==n;)null!==(e=n.alternate)&&null===Mo(e)&&(o=n),n=n.sibling;null===(n=o)?(o=t.child,t.child=null):(o=n.sibling,n.sibling=null),ui(t,!1,o,n,a);break;case"backwards":for(n=null,o=t.child,t.child=null;null!==o;){if(null!==(e=o.alternate)&&null===Mo(e)){t.child=o;break}e=o.sibling,o.sibling=n,n=o,o=e}ui(t,!0,n,null,a);break;case"together":ui(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function fi(e,t,n){if(null!==e&&(t.dependencies=e.dependencies),dc|=t.lanes,0===(n&t.childLanes)){if(null===e)return null;if(ki(e,t,n,!1),0===(n&t.childLanes))return null}if(null!==e&&t.child!==e.child)throw Error(l(153));if(null!==t.child){for(n=Ds(e=t.child,e.pendingProps),t.child=n,n.return=t;null!==e.sibling;)e=e.sibling,(n=n.sibling=Ds(e,e.pendingProps)).return=t;n.sibling=null}return t.child}function pi(e,t){return 0!==(e.lanes&t)||!(null===(e=e.dependencies)||!Si(e))}function mi(e,t,n){if(null!==e)if(e.memoizedProps!==t.pendingProps)Bl=!0;else{if(!pi(e,n)&&0===(128&t.flags))return Bl=!1,function(e,t,n){switch(t.tag){case 3:J(t,t.stateNode.containerInfo),yi(t,Ho,e.memoizedState.cache),oo();break;case 27:case 5:te(t);break;case 4:J(t,t.stateNode.containerInfo);break;case 10:yi(t,t.type,t.memoizedProps.value);break;case 13:var r=t.memoizedState;if(null!==r)return null!==r.dehydrated?(Ro(t),t.flags|=128,null):0!==(n&t.child.childLanes)?ai(e,t,n):(Ro(t),null!==(e=fi(e,t,n))?e.sibling:null);Ro(t);break;case 19:var o=0!==(128&e.flags);if((r=0!==(n&t.childLanes))||(ki(e,t,n,!1),r=0!==(n&t.childLanes)),o){if(r)return di(e,t,n);t.flags|=128}if(null!==(o=t.memoizedState)&&(o.rendering=null,o.tail=null,o.lastEffect=null),Q(Lo,Lo.current),r)break;return null;case 22:case 23:return t.lanes=0,Gl(e,t,n);case 24:yi(t,Ho,e.memoizedState.cache)}return fi(e,t,n)}(e,t,n);Bl=0!==(131072&e.flags)}else Bl=!1,Yr&&0!==(1048576&t.flags)&&$r(t,Ir,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var r=t.elementType,o=r._init;if(r=o(r._payload),t.type=r,"function"!==typeof r){if(void 0!==r&&null!==r){if((o=r.$$typeof)===v){t.tag=11,t=ql(null,t,r,e,n);break e}if(o===w){t.tag=14,t=Kl(null,t,r,e,n);break e}}throw t=j(r)||r,Error(l(306,t,""))}Ms(r)?(e=zl(r,e),t.tag=1,t=ei(null,t,r,e,n)):(t.tag=0,t=Zl(null,t,r,e,n))}return t;case 0:return Zl(e,t,t.type,t.pendingProps,n);case 1:return ei(e,t,r=t.type,o=zl(r,t.pendingProps),n);case 3:e:{if(J(t,t.stateNode.containerInfo),null===e)throw Error(l(387));var a=t.pendingProps;r=(o=t.memoizedState).element,Ti(e,t),Di(t,a,null,n);var i=t.memoizedState;if(a=i.cache,yi(t,Ho,a),a!==o.cache&&xi(t,[Ho],n,!0),Mi(),a=i.element,o.isDehydrated){if(o={element:a,isDehydrated:!1,cache:i.cache},t.updateQueue.baseState=o,t.memoizedState=o,256&t.flags){t=ti(e,t,a,n);break e}if(a!==r){ao(r=Or(Error(l(424)),t)),t=ti(e,t,a,n);break e}for(Gr=id(t.stateNode.containerInfo.firstChild),Qr=t,Yr=!0,Xr=null,Zr=!0,n=So(t,null,a,n),t.child=n;n;)n.flags=-3&n.flags|4096,n=n.sibling}else{if(oo(),a===r){t=fi(e,t,n);break e}$l(e,t,a,n)}t=t.child}return t;case 26:return Xl(e,t),null===e?(n=gd(t.type,null,t.pendingProps,null))?t.memoizedState=n:Yr||(n=t.type,e=t.pendingProps,(r=Gu(X.current).createElement(n))[Ie]=t,r[Fe]=e,qu(r,n,e),Xe(r),t.stateNode=r):t.memoizedState=gd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return te(t),null===e&&Yr&&(r=t.stateNode=cd(t.type,t.pendingProps,X.current),Qr=t,Zr=!0,Gr=id(r.firstChild)),r=t.pendingProps.children,null!==e||Yr?$l(e,t,r,n):t.child=ko(t,null,r,n),Xl(e,t),t.child;case 5:return null===e&&Yr&&((o=r=Gr)&&(null!==(r=function(e,t,n,r){for(;1===e.nodeType;){var o=n;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!r&&("INPUT"!==e.nodeName||"hidden"!==e.type))break}else if(r){if(!e[$e])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if("stylesheet"===(a=e.getAttribute("rel"))&&e.hasAttribute("data-precedence"))break;if(a!==o.rel||e.getAttribute("href")!==(null==o.href?null:o.href)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin)||e.getAttribute("title")!==(null==o.title?null:o.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(((a=e.getAttribute("src"))!==(null==o.src?null:o.src)||e.getAttribute("type")!==(null==o.type?null:o.type)||e.getAttribute("crossorigin")!==(null==o.crossOrigin?null:o.crossOrigin))&&a&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else{if("input"!==t||"hidden"!==e.type)return e;var a=null==o.name?null:""+o.name;if("hidden"===o.type&&e.getAttribute("name")===a)return e}if(null===(e=id(e.nextSibling)))break}return null}(r,t.type,t.pendingProps,Zr))?(t.stateNode=r,Qr=t,Gr=id(r.firstChild),Zr=!1,o=!0):o=!1),o||eo(t)),te(t),o=t.type,a=t.pendingProps,i=null!==e?e.memoizedProps:null,r=a.children,Zu(o,a)?r=null:null!==i&&Zu(o,i)&&(t.flags|=32),null!==t.memoizedState&&(o=fa(e,t,ha,null,null,n),Md._currentValue=o),Xl(e,t),$l(e,t,r,n),t.child;case 6:return null===e&&Yr&&((e=n=Gr)&&(null!==(n=function(e,t,n){if(""===t)return null;for(;3!==e.nodeType;){if((1!==e.nodeType||"INPUT"!==e.nodeName||"hidden"!==e.type)&&!n)return null;if(null===(e=id(e.nextSibling)))return null}return e}(n,t.pendingProps,Zr))?(t.stateNode=n,Qr=t,Gr=null,e=!0):e=!1),e||eo(t)),null;case 13:return ai(e,t,n);case 4:return J(t,t.stateNode.containerInfo),r=t.pendingProps,null===e?t.child=ko(t,null,r,n):$l(e,t,r,n),t.child;case 11:return ql(e,t,t.type,t.pendingProps,n);case 7:return $l(e,t,t.pendingProps,n),t.child;case 8:case 12:return $l(e,t,t.pendingProps.children,n),t.child;case 10:return r=t.pendingProps,yi(t,t.type,r.value),$l(e,t,r.children,n),t.child;case 9:return o=t.type._context,r=t.pendingProps.children,Ni(t),r=r(o=Ei(o)),t.flags|=1,$l(e,t,r,n),t.child;case 14:return Kl(e,t,t.type,t.pendingProps,n);case 15:return Ql(e,t,t.type,t.pendingProps,n);case 19:return di(e,t,n);case 22:return Gl(e,t,n);case 24:return Ni(t),r=Ei(Ho),null===e?(null===(o=Yo())&&(o=tc,a=Uo(),o.pooledCache=a,a.refCount++,null!==a&&(o.pooledCacheLanes|=n),o=a),t.memoizedState={parent:r,cache:o},_i(t),yi(t,Ho,o)):(0!==(e.lanes&n)&&(Ti(e,t),Di(t,null,null,n),Mi()),o=e.memoizedState,a=t.memoizedState,o.parent!==r?(o={parent:r,cache:r},t.memoizedState=o,0===t.lanes&&(t.memoizedState=t.updateQueue.baseState=o),yi(t,Ho,r)):(r=a.cache,yi(t,Ho,r),r!==o.cache&&xi(t,[Ho],n,!0))),$l(e,t,t.pendingProps.children,n),t.child;case 29:throw t.pendingProps}throw Error(l(156,t.tag))}var hi=q(null),gi=null,vi=null;function yi(e,t,n){Q(hi,t._currentValue),t._currentValue=n}function bi(e){e._currentValue=hi.current,K(hi)}function wi(e,t,n){for(;null!==e;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,null!==r&&(r.childLanes|=t)):null!==r&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function xi(e,t,n,r){var o=e.child;for(null!==o&&(o.return=e);null!==o;){var a=o.dependencies;if(null!==a){var i=o.child;a=a.firstContext;e:for(;null!==a;){var s=a;a=o;for(var c=0;c<t.length;c++)if(s.context===t[c]){a.lanes|=n,null!==(s=a.alternate)&&(s.lanes|=n),wi(a.return,n,e),r||(i=null);break e}a=s.next}}else if(18===o.tag){if(null===(i=o.return))throw Error(l(341));i.lanes|=n,null!==(a=i.alternate)&&(a.lanes|=n),wi(i,n,e),i=null}else i=o.child;if(null!==i)i.return=o;else for(i=o;null!==i;){if(i===e){i=null;break}if(null!==(o=i.sibling)){o.return=i.return,i=o;break}i=i.return}o=i}}function ki(e,t,n,r){e=null;for(var o=t,a=!1;null!==o;){if(!a)if(0!==(524288&o.flags))a=!0;else if(0!==(262144&o.flags))break;if(10===o.tag){var i=o.alternate;if(null===i)throw Error(l(387));if(null!==(i=i.memoizedProps)){var s=o.type;Qn(o.pendingProps.value,i.value)||(null!==e?e.push(s):e=[s])}}else if(o===Z.current){if(null===(i=o.alternate))throw Error(l(387));i.memoizedState.memoizedState!==o.memoizedState.memoizedState&&(null!==e?e.push(Md):e=[Md])}o=o.return}null!==e&&xi(t,e,n,r),t.flags|=262144}function Si(e){for(e=e.firstContext;null!==e;){if(!Qn(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function Ni(e){gi=e,vi=null,null!==(e=e.dependencies)&&(e.firstContext=null)}function Ei(e){return ji(gi,e)}function Ci(e,t){return null===gi&&Ni(e),ji(e,t)}function ji(e,t){var n=t._currentValue;if(t={context:t,memoizedValue:n,next:null},null===vi){if(null===e)throw Error(l(308));vi=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else vi=vi.next=t;return n}var Pi=!1;function _i(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Ti(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Ri(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Ai(e,t,n){var r=e.updateQueue;if(null===r)return null;if(r=r.shared,0!==(2&ec)){var o=r.pending;return null===o?t.next=t:(t.next=o.next,o.next=t),r.pending=t,t=Rr(e),Tr(e,null,n),t}return jr(e,r,t,n),Rr(e)}function zi(e,t,n){if(null!==(t=t.updateQueue)&&(t=t.shared,0!==(4194176&n))){var r=t.lanes;n|=r&=e.pendingLanes,t.lanes=n,Oe(e,n)}}function Oi(e,t){var n=e.updateQueue,r=e.alternate;if(null!==r&&n===(r=r.updateQueue)){var o=null,a=null;if(null!==(n=n.firstBaseUpdate)){do{var l={lane:n.lane,tag:n.tag,payload:n.payload,callback:null,next:null};null===a?o=a=l:a=a.next=l,n=n.next}while(null!==n);null===a?o=a=t:a=a.next=t}else o=a=t;return n={baseState:r.baseState,firstBaseUpdate:o,lastBaseUpdate:a,shared:r.shared,callbacks:r.callbacks},void(e.updateQueue=n)}null===(e=n.lastBaseUpdate)?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}var Li=!1;function Mi(){if(Li){if(null!==qo)throw qo}}function Di(e,t,n,r){Li=!1;var o=e.updateQueue;Pi=!1;var a=o.firstBaseUpdate,l=o.lastBaseUpdate,i=o.shared.pending;if(null!==i){o.shared.pending=null;var s=i,c=s.next;s.next=null,null===l?a=c:l.next=c,l=s;var u=e.alternate;null!==u&&((i=(u=u.updateQueue).lastBaseUpdate)!==l&&(null===i?u.firstBaseUpdate=c:i.next=c,u.lastBaseUpdate=s))}if(null!==a){var d=o.baseState;for(l=0,u=c=s=null,i=a;;){var f=-536870913&i.lane,p=f!==i.lane;if(p?(rc&f)===f:(r&f)===f){0!==f&&f===$o&&(Li=!0),null!==u&&(u=u.next={lane:0,tag:i.tag,payload:i.payload,callback:null,next:null});e:{var m=e,h=i;f=t;var g=n;switch(h.tag){case 1:if("function"===typeof(m=h.payload)){d=m.call(g,d,f);break e}d=m;break e;case 3:m.flags=-65537&m.flags|128;case 0:if(null===(f="function"===typeof(m=h.payload)?m.call(g,d,f):m)||void 0===f)break e;d=R({},d,f);break e;case 2:Pi=!0}}null!==(f=i.callback)&&(e.flags|=64,p&&(e.flags|=8192),null===(p=o.callbacks)?o.callbacks=[f]:p.push(f))}else p={lane:f,tag:i.tag,payload:i.payload,callback:i.callback,next:null},null===u?(c=u=p,s=d):u=u.next=p,l|=f;if(null===(i=i.next)){if(null===(i=o.shared.pending))break;i=(p=i).next,p.next=null,o.lastBaseUpdate=p,o.shared.pending=null}}null===u&&(s=d),o.baseState=s,o.firstBaseUpdate=c,o.lastBaseUpdate=u,null===a&&(o.shared.lanes=0),dc|=l,e.lanes=l,e.memoizedState=d}}function Ii(e,t){if("function"!==typeof e)throw Error(l(191,e));e.call(t)}function Fi(e,t){var n=e.callbacks;if(null!==n)for(e.callbacks=null,e=0;e<n.length;e++)Ii(n[e],t)}function Hi(e,t){try{var n=t.updateQueue,r=null!==n?n.lastEffect:null;if(null!==r){var o=r.next;n=o;do{if((n.tag&e)===e){r=void 0;var a=n.create,l=n.inst;r=a(),l.destroy=r}n=n.next}while(n!==o)}}catch(i){ru(t,t.return,i)}}function Ui(e,t,n){try{var r=t.updateQueue,o=null!==r?r.lastEffect:null;if(null!==o){var a=o.next;r=a;do{if((r.tag&e)===e){var l=r.inst,i=l.destroy;if(void 0!==i){l.destroy=void 0,o=t;var s=n;try{i()}catch(c){ru(o,s,c)}}}r=r.next}while(r!==a)}}catch(c){ru(t,t.return,c)}}function Wi(e){var t=e.updateQueue;if(null!==t){var n=e.stateNode;try{Fi(t,n)}catch(r){ru(e,e.return,r)}}}function Vi(e,t,n){n.props=zl(e.type,e.memoizedProps),n.state=e.memoizedState;try{n.componentWillUnmount()}catch(r){ru(e,t,r)}}function Bi(e,t){try{var n=e.ref;if(null!==n){var r=e.stateNode;switch(e.tag){case 26:case 27:case 5:var o=r;break;default:o=r}"function"===typeof n?e.refCleanup=n(o):n.current=o}}catch(a){ru(e,t,a)}}function $i(e,t){var n=e.ref,r=e.refCleanup;if(null!==n)if("function"===typeof r)try{r()}catch(o){ru(e,t,o)}finally{e.refCleanup=null,null!=(e=e.alternate)&&(e.refCleanup=null)}else if("function"===typeof n)try{n(null)}catch(a){ru(e,t,a)}else n.current=null}function qi(e){var t=e.type,n=e.memoizedProps,r=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":n.autoFocus&&r.focus();break e;case"img":n.src?r.src=n.src:n.srcSet&&(r.srcset=n.srcSet)}}catch(o){ru(e,e.return,o)}}function Ki(e,t,n){try{var r=e.stateNode;!function(e,t,n,r){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var o=null,a=null,i=null,s=null,c=null,u=null,d=null;for(m in n){var f=n[m];if(n.hasOwnProperty(m)&&null!=f)switch(m){case"checked":case"value":break;case"defaultValue":c=f;default:r.hasOwnProperty(m)||Bu(e,t,m,null,r,f)}}for(var p in r){var m=r[p];if(f=n[p],r.hasOwnProperty(p)&&(null!=m||null!=f))switch(p){case"type":a=m;break;case"name":o=m;break;case"checked":u=m;break;case"defaultChecked":d=m;break;case"value":i=m;break;case"defaultValue":s=m;break;case"children":case"dangerouslySetInnerHTML":if(null!=m)throw Error(l(137,t));break;default:m!==f&&Bu(e,t,p,m,r,f)}}return void gt(e,i,s,c,u,d,a,o);case"select":for(a in m=i=s=p=null,n)if(c=n[a],n.hasOwnProperty(a)&&null!=c)switch(a){case"value":break;case"multiple":m=c;default:r.hasOwnProperty(a)||Bu(e,t,a,null,r,c)}for(o in r)if(a=r[o],c=n[o],r.hasOwnProperty(o)&&(null!=a||null!=c))switch(o){case"value":p=a;break;case"defaultValue":s=a;break;case"multiple":i=a;default:a!==c&&Bu(e,t,o,a,r,c)}return t=s,n=i,r=m,void(null!=p?bt(e,!!n,p,!1):!!r!==!!n&&(null!=t?bt(e,!!n,t,!0):bt(e,!!n,n?[]:"",!1)));case"textarea":for(s in m=p=null,n)if(o=n[s],n.hasOwnProperty(s)&&null!=o&&!r.hasOwnProperty(s))switch(s){case"value":case"children":break;default:Bu(e,t,s,null,r,o)}for(i in r)if(o=r[i],a=n[i],r.hasOwnProperty(i)&&(null!=o||null!=a))switch(i){case"value":p=o;break;case"defaultValue":m=o;break;case"children":break;case"dangerouslySetInnerHTML":if(null!=o)throw Error(l(91));break;default:o!==a&&Bu(e,t,i,o,r,a)}return void wt(e,p,m);case"option":for(var h in n)if(p=n[h],n.hasOwnProperty(h)&&null!=p&&!r.hasOwnProperty(h))if("selected"===h)e.selected=!1;else Bu(e,t,h,null,r,p);for(c in r)if(p=r[c],m=n[c],r.hasOwnProperty(c)&&p!==m&&(null!=p||null!=m))if("selected"===c)e.selected=p&&"function"!==typeof p&&"symbol"!==typeof p;else Bu(e,t,c,p,r,m);return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var g in n)p=n[g],n.hasOwnProperty(g)&&null!=p&&!r.hasOwnProperty(g)&&Bu(e,t,g,null,r,p);for(u in r)if(p=r[u],m=n[u],r.hasOwnProperty(u)&&p!==m&&(null!=p||null!=m))switch(u){case"children":case"dangerouslySetInnerHTML":if(null!=p)throw Error(l(137,t));break;default:Bu(e,t,u,p,r,m)}return;default:if(Ct(t)){for(var v in n)p=n[v],n.hasOwnProperty(v)&&void 0!==p&&!r.hasOwnProperty(v)&&$u(e,t,v,void 0,r,p);for(d in r)p=r[d],m=n[d],!r.hasOwnProperty(d)||p===m||void 0===p&&void 0===m||$u(e,t,d,p,r,m);return}}for(var y in n)p=n[y],n.hasOwnProperty(y)&&null!=p&&!r.hasOwnProperty(y)&&Bu(e,t,y,null,r,p);for(f in r)p=r[f],m=n[f],!r.hasOwnProperty(f)||p===m||null==p&&null==m||Bu(e,t,f,p,r,m)}(r,e.type,n,t),r[Fe]=t}catch(o){ru(e,e.return,o)}}function Qi(e){return 5===e.tag||3===e.tag||26===e.tag||27===e.tag||4===e.tag}function Gi(e){e:for(;;){for(;null===e.sibling;){if(null===e.return||Qi(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;5!==e.tag&&6!==e.tag&&27!==e.tag&&18!==e.tag;){if(2&e.flags)continue e;if(null===e.child||4===e.tag)continue e;e.child.return=e,e=e.child}if(!(2&e.flags))return e.stateNode}}function Yi(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?8===n.nodeType?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(8===n.nodeType?(t=n.parentNode).insertBefore(e,n):(t=n).appendChild(e),null!==(n=n._reactRootContainer)&&void 0!==n||null!==t.onclick||(t.onclick=Vu));else if(4!==r&&27!==r&&null!==(e=e.child))for(Yi(e,t,n),e=e.sibling;null!==e;)Yi(e,t,n),e=e.sibling}function Xi(e,t,n){var r=e.tag;if(5===r||6===r)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(4!==r&&27!==r&&null!==(e=e.child))for(Xi(e,t,n),e=e.sibling;null!==e;)Xi(e,t,n),e=e.sibling}var Zi=!1,Ji=!1,es=!1,ts="function"===typeof WeakSet?WeakSet:Set,ns=null,rs=!1;function os(e,t,n){var r=n.flags;switch(n.tag){case 0:case 11:case 15:vs(e,n),4&r&&Hi(5,n);break;case 1:if(vs(e,n),4&r)if(e=n.stateNode,null===t)try{e.componentDidMount()}catch(i){ru(n,n.return,i)}else{var o=zl(n.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(o,t,e.__reactInternalSnapshotBeforeUpdate)}catch(s){ru(n,n.return,s)}}64&r&&Wi(n),512&r&&Bi(n,n.return);break;case 3:if(vs(e,n),64&r&&null!==(r=n.updateQueue)){if(e=null,null!==n.child)switch(n.child.tag){case 27:case 5:case 1:e=n.child.stateNode}try{Fi(r,e)}catch(i){ru(n,n.return,i)}}break;case 26:vs(e,n),512&r&&Bi(n,n.return);break;case 27:case 5:vs(e,n),null===t&&4&r&&qi(n),512&r&&Bi(n,n.return);break;case 12:default:vs(e,n);break;case 13:vs(e,n),4&r&&us(e,n);break;case 22:if(!(o=null!==n.memoizedState||Zi)){t=null!==t&&null!==t.memoizedState||Ji;var a=Zi,l=Ji;Zi=o,(Ji=t)&&!l?bs(e,n,0!==(8772&n.subtreeFlags)):vs(e,n),Zi=a,Ji=l}512&r&&("manual"===n.memoizedProps.mode?Bi(n,n.return):$i(n,n.return))}}function as(e){var t=e.alternate;null!==t&&(e.alternate=null,as(t)),e.child=null,e.deletions=null,e.sibling=null,5===e.tag&&(null!==(t=e.stateNode)&&qe(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ls=null,is=!1;function ss(e,t,n){for(n=n.child;null!==n;)cs(e,t,n),n=n.sibling}function cs(e,t,n){if(ye&&"function"===typeof ye.onCommitFiberUnmount)try{ye.onCommitFiberUnmount(ve,n)}catch(l){}switch(n.tag){case 26:Ji||$i(n,t),ss(e,t,n),n.memoizedState?n.memoizedState.count--:n.stateNode&&(n=n.stateNode).parentNode.removeChild(n);break;case 27:Ji||$i(n,t);var r=ls,o=is;for(ls=n.stateNode,ss(e,t,n),t=(n=n.stateNode).attributes;t.length;)n.removeAttributeNode(t[0]);qe(n),ls=r,is=o;break;case 5:Ji||$i(n,t);case 6:o=ls;var a=is;if(ls=null,ss(e,t,n),is=a,null!==(ls=o))if(is)try{e=ls,r=n.stateNode,8===e.nodeType?e.parentNode.removeChild(r):e.removeChild(r)}catch(i){ru(n,t,i)}else try{ls.removeChild(n.stateNode)}catch(i){ru(n,t,i)}break;case 18:null!==ls&&(is?(t=ls,n=n.stateNode,8===t.nodeType?ad(t.parentNode,n):1===t.nodeType&&ad(t,n),gf(t)):ad(ls,n.stateNode));break;case 4:r=ls,o=is,ls=n.stateNode.containerInfo,is=!0,ss(e,t,n),ls=r,is=o;break;case 0:case 11:case 14:case 15:Ji||Ui(2,n,t),Ji||Ui(4,n,t),ss(e,t,n);break;case 1:Ji||($i(n,t),"function"===typeof(r=n.stateNode).componentWillUnmount&&Vi(n,t,r)),ss(e,t,n);break;case 21:ss(e,t,n);break;case 22:Ji||$i(n,t),Ji=(r=Ji)||null!==n.memoizedState,ss(e,t,n),Ji=r;break;default:ss(e,t,n)}}function us(e,t){if(null===t.memoizedState&&(null!==(e=t.alternate)&&(null!==(e=e.memoizedState)&&null!==(e=e.dehydrated))))try{gf(e)}catch(n){ru(t,t.return,n)}}function ds(e,t){var n=function(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return null===t&&(t=e.stateNode=new ts),t;case 22:return null===(t=(e=e.stateNode)._retryCache)&&(t=e._retryCache=new ts),t;default:throw Error(l(435,e.tag))}}(e);t.forEach((function(t){var r=su.bind(null,e,t);n.has(t)||(n.add(t),t.then(r,r))}))}function fs(e,t){var n=t.deletions;if(null!==n)for(var r=0;r<n.length;r++){var o=n[r],a=e,i=t,s=i;e:for(;null!==s;){switch(s.tag){case 27:case 5:ls=s.stateNode,is=!1;break e;case 3:case 4:ls=s.stateNode.containerInfo,is=!0;break e}s=s.return}if(null===ls)throw Error(l(160));cs(a,i,o),ls=null,is=!1,null!==(a=o.alternate)&&(a.return=null),o.return=null}if(13878&t.subtreeFlags)for(t=t.child;null!==t;)ms(t,e),t=t.sibling}var ps=null;function ms(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:fs(t,e),hs(e),4&r&&(Ui(3,e,e.return),Hi(3,e),Ui(5,e,e.return));break;case 1:fs(t,e),hs(e),512&r&&(Ji||null===n||$i(n,n.return)),64&r&&Zi&&(null!==(e=e.updateQueue)&&(null!==(r=e.callbacks)&&(n=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=null===n?r:n.concat(r))));break;case 26:var o=ps;if(fs(t,e),hs(e),512&r&&(Ji||null===n||$i(n,n.return)),4&r){var a=null!==n?n.memoizedState:null;if(r=e.memoizedState,null===n)if(null===r)if(null===e.stateNode){e:{r=e.type,n=e.memoizedProps,o=o.ownerDocument||o;t:switch(r){case"title":(!(a=o.getElementsByTagName("title")[0])||a[$e]||a[Ie]||"http://www.w3.org/2000/svg"===a.namespaceURI||a.hasAttribute("itemprop"))&&(a=o.createElement(r),o.head.insertBefore(a,o.querySelector("head > title"))),qu(a,r,n),a[Ie]=e,Xe(a),r=a;break e;case"link":var i=jd("link","href",o).get(r+(n.href||""));if(i)for(var s=0;s<i.length;s++)if((a=i[s]).getAttribute("href")===(null==n.href?null:n.href)&&a.getAttribute("rel")===(null==n.rel?null:n.rel)&&a.getAttribute("title")===(null==n.title?null:n.title)&&a.getAttribute("crossorigin")===(null==n.crossOrigin?null:n.crossOrigin)){i.splice(s,1);break t}qu(a=o.createElement(r),r,n),o.head.appendChild(a);break;case"meta":if(i=jd("meta","content",o).get(r+(n.content||"")))for(s=0;s<i.length;s++)if((a=i[s]).getAttribute("content")===(null==n.content?null:""+n.content)&&a.getAttribute("name")===(null==n.name?null:n.name)&&a.getAttribute("property")===(null==n.property?null:n.property)&&a.getAttribute("http-equiv")===(null==n.httpEquiv?null:n.httpEquiv)&&a.getAttribute("charset")===(null==n.charSet?null:n.charSet)){i.splice(s,1);break t}qu(a=o.createElement(r),r,n),o.head.appendChild(a);break;default:throw Error(l(468,r))}a[Ie]=e,Xe(a),r=a}e.stateNode=r}else Pd(o,e.type,e.stateNode);else e.stateNode=kd(o,r,e.memoizedProps);else a!==r?(null===a?null!==n.stateNode&&(n=n.stateNode).parentNode.removeChild(n):a.count--,null===r?Pd(o,e.type,e.stateNode):kd(o,r,e.memoizedProps)):null===r&&null!==e.stateNode&&Ki(e,e.memoizedProps,n.memoizedProps)}break;case 27:if(4&r&&null===e.alternate){o=e.stateNode,a=e.memoizedProps;try{for(var c=o.firstChild;c;){var u=c.nextSibling,d=c.nodeName;c[$e]||"HEAD"===d||"BODY"===d||"SCRIPT"===d||"STYLE"===d||"LINK"===d&&"stylesheet"===c.rel.toLowerCase()||o.removeChild(c),c=u}for(var f=e.type,p=o.attributes;p.length;)o.removeAttributeNode(p[0]);qu(o,f,a),o[Ie]=e,o[Fe]=a}catch(h){ru(e,e.return,h)}}case 5:if(fs(t,e),hs(e),512&r&&(Ji||null===n||$i(n,n.return)),32&e.flags){o=e.stateNode;try{kt(o,"")}catch(h){ru(e,e.return,h)}}4&r&&null!=e.stateNode&&Ki(e,o=e.memoizedProps,null!==n?n.memoizedProps:o),1024&r&&(es=!0);break;case 6:if(fs(t,e),hs(e),4&r){if(null===e.stateNode)throw Error(l(162));r=e.memoizedProps,n=e.stateNode;try{n.nodeValue=r}catch(h){ru(e,e.return,h)}}break;case 3:if(Cd=null,o=ps,ps=fd(t.containerInfo),fs(t,e),ps=o,hs(e),4&r&&null!==n&&n.memoizedState.isDehydrated)try{gf(t.containerInfo)}catch(h){ru(e,e.return,h)}es&&(es=!1,gs(e));break;case 4:r=ps,ps=fd(e.stateNode.containerInfo),fs(t,e),hs(e),ps=r;break;case 12:fs(t,e),hs(e);break;case 13:fs(t,e),hs(e),8192&e.child.flags&&null!==e.memoizedState!==(null!==n&&null!==n.memoizedState)&&(bc=se()),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,ds(e,r)));break;case 22:if(512&r&&(Ji||null===n||$i(n,n.return)),c=null!==e.memoizedState,u=null!==n&&null!==n.memoizedState,Zi=(d=Zi)||c,Ji=(f=Ji)||u,fs(t,e),Ji=f,Zi=d,hs(e),(t=e.stateNode)._current=e,t._visibility&=-3,t._visibility|=2&t._pendingVisibility,8192&r&&(t._visibility=c?-2&t._visibility:1|t._visibility,c&&(t=Zi||Ji,null===n||u||t||ys(e)),null===e.memoizedProps||"manual"!==e.memoizedProps.mode))e:for(n=null,t=e;;){if(5===t.tag||26===t.tag||27===t.tag){if(null===n){u=n=t;try{if(o=u.stateNode,c)"function"===typeof(a=o.style).setProperty?a.setProperty("display","none","important"):a.display="none";else{i=u.stateNode;var m=void 0!==(s=u.memoizedProps.style)&&null!==s&&s.hasOwnProperty("display")?s.display:null;i.style.display=null==m||"boolean"===typeof m?"":(""+m).trim()}}catch(h){ru(u,u.return,h)}}}else if(6===t.tag){if(null===n){u=t;try{u.stateNode.nodeValue=c?"":u.memoizedProps}catch(h){ru(u,u.return,h)}}}else if((22!==t.tag&&23!==t.tag||null===t.memoizedState||t===e)&&null!==t.child){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;null===t.sibling;){if(null===t.return||t.return===e)break e;n===t&&(n=null),t=t.return}n===t&&(n=null),t.sibling.return=t.return,t=t.sibling}4&r&&(null!==(r=e.updateQueue)&&(null!==(n=r.retryQueue)&&(r.retryQueue=null,ds(e,n))));break;case 19:fs(t,e),hs(e),4&r&&(null!==(r=e.updateQueue)&&(e.updateQueue=null,ds(e,r)));break;case 21:break;default:fs(t,e),hs(e)}}function hs(e){var t=e.flags;if(2&t){try{if(27!==e.tag){e:{for(var n=e.return;null!==n;){if(Qi(n)){var r=n;break e}n=n.return}throw Error(l(160))}switch(r.tag){case 27:var o=r.stateNode;Xi(e,Gi(e),o);break;case 5:var a=r.stateNode;32&r.flags&&(kt(a,""),r.flags&=-33),Xi(e,Gi(e),a);break;case 3:case 4:var i=r.stateNode.containerInfo;Yi(e,Gi(e),i);break;default:throw Error(l(161))}}}catch(s){ru(e,e.return,s)}e.flags&=-3}4096&t&&(e.flags&=-4097)}function gs(e){if(1024&e.subtreeFlags)for(e=e.child;null!==e;){var t=e;gs(t),5===t.tag&&1024&t.flags&&t.stateNode.reset(),e=e.sibling}}function vs(e,t){if(8772&t.subtreeFlags)for(t=t.child;null!==t;)os(e,t.alternate,t),t=t.sibling}function ys(e){for(e=e.child;null!==e;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ui(4,t,t.return),ys(t);break;case 1:$i(t,t.return);var n=t.stateNode;"function"===typeof n.componentWillUnmount&&Vi(t,t.return,n),ys(t);break;case 26:case 27:case 5:$i(t,t.return),ys(t);break;case 22:$i(t,t.return),null===t.memoizedState&&ys(t);break;default:ys(t)}e=e.sibling}}function bs(e,t,n){for(n=n&&0!==(8772&t.subtreeFlags),t=t.child;null!==t;){var r=t.alternate,o=e,a=t,l=a.flags;switch(a.tag){case 0:case 11:case 15:bs(o,a,n),Hi(4,a);break;case 1:if(bs(o,a,n),"function"===typeof(o=(r=a).stateNode).componentDidMount)try{o.componentDidMount()}catch(c){ru(r,r.return,c)}if(null!==(o=(r=a).updateQueue)){var i=r.stateNode;try{var s=o.shared.hiddenCallbacks;if(null!==s)for(o.shared.hiddenCallbacks=null,o=0;o<s.length;o++)Ii(s[o],i)}catch(c){ru(r,r.return,c)}}n&&64&l&&Wi(a),Bi(a,a.return);break;case 26:case 27:case 5:bs(o,a,n),n&&null===r&&4&l&&qi(a),Bi(a,a.return);break;case 12:default:bs(o,a,n);break;case 13:bs(o,a,n),n&&4&l&&us(o,a);break;case 22:null===a.memoizedState&&bs(o,a,n),Bi(a,a.return)}t=t.sibling}}function ws(e,t){var n=null;null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),e=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(e=t.memoizedState.cachePool.pool),e!==n&&(null!=e&&e.refCount++,null!=n&&Wo(n))}function xs(e,t){e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Wo(e))}function ks(e,t,n,r){if(10256&t.subtreeFlags)for(t=t.child;null!==t;)Ss(e,t,n,r),t=t.sibling}function Ss(e,t,n,r){var o=t.flags;switch(t.tag){case 0:case 11:case 15:ks(e,t,n,r),2048&o&&Hi(9,t);break;case 3:ks(e,t,n,r),2048&o&&(e=null,null!==t.alternate&&(e=t.alternate.memoizedState.cache),(t=t.memoizedState.cache)!==e&&(t.refCount++,null!=e&&Wo(e)));break;case 12:if(2048&o){ks(e,t,n,r),e=t.stateNode;try{var a=t.memoizedProps,l=a.id,i=a.onPostCommit;"function"===typeof i&&i(l,null===t.alternate?"mount":"update",e.passiveEffectDuration,-0)}catch(s){ru(t,t.return,s)}}else ks(e,t,n,r);break;case 23:break;case 22:a=t.stateNode,null!==t.memoizedState?4&a._visibility?ks(e,t,n,r):Es(e,t):4&a._visibility?ks(e,t,n,r):(a._visibility|=4,Ns(e,t,n,r,0!==(10256&t.subtreeFlags))),2048&o&&ws(t.alternate,t);break;case 24:ks(e,t,n,r),2048&o&&xs(t.alternate,t);break;default:ks(e,t,n,r)}}function Ns(e,t,n,r,o){for(o=o&&0!==(10256&t.subtreeFlags),t=t.child;null!==t;){var a=e,l=t,i=n,s=r,c=l.flags;switch(l.tag){case 0:case 11:case 15:Ns(a,l,i,s,o),Hi(8,l);break;case 23:break;case 22:var u=l.stateNode;null!==l.memoizedState?4&u._visibility?Ns(a,l,i,s,o):Es(a,l):(u._visibility|=4,Ns(a,l,i,s,o)),o&&2048&c&&ws(l.alternate,l);break;case 24:Ns(a,l,i,s,o),o&&2048&c&&xs(l.alternate,l);break;default:Ns(a,l,i,s,o)}t=t.sibling}}function Es(e,t){if(10256&t.subtreeFlags)for(t=t.child;null!==t;){var n=e,r=t,o=r.flags;switch(r.tag){case 22:Es(n,r),2048&o&&ws(r.alternate,r);break;case 24:Es(n,r),2048&o&&xs(r.alternate,r);break;default:Es(n,r)}t=t.sibling}}var Cs=8192;function js(e){if(e.subtreeFlags&Cs)for(e=e.child;null!==e;)Ps(e),e=e.sibling}function Ps(e){switch(e.tag){case 26:js(e),e.flags&Cs&&null!==e.memoizedState&&function(e,t,n){if(null===Td)throw Error(l(475));var r=Td;if("stylesheet"===t.type&&("string"!==typeof n.media||!1!==matchMedia(n.media).matches)&&0===(4&t.state.loading)){if(null===t.instance){var o=vd(n.href),a=e.querySelector(yd(o));if(a)return null!==(e=a._p)&&"object"===typeof e&&"function"===typeof e.then&&(r.count++,r=Ad.bind(r),e.then(r,r)),t.state.loading|=4,t.instance=a,void Xe(a);a=e.ownerDocument||e,n=bd(n),(o=ud.get(o))&&Nd(n,o),Xe(a=a.createElement("link"));var i=a;i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),qu(a,"link",n),t.instance=a}null===r.stylesheets&&(r.stylesheets=new Map),r.stylesheets.set(t,e),(e=t.state.preload)&&0===(3&t.state.loading)&&(r.count++,t=Ad.bind(r),e.addEventListener("load",t),e.addEventListener("error",t))}}(ps,e.memoizedState,e.memoizedProps);break;case 5:default:js(e);break;case 3:case 4:var t=ps;ps=fd(e.stateNode.containerInfo),js(e),ps=t;break;case 22:null===e.memoizedState&&(null!==(t=e.alternate)&&null!==t.memoizedState?(t=Cs,Cs=16777216,js(e),Cs=t):js(e))}}function _s(e){var t=e.alternate;if(null!==t&&null!==(e=t.child)){t.child=null;do{t=e.sibling,e.sibling=null,e=t}while(null!==e)}}function Ts(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];ns=r,zs(r,e)}_s(e)}if(10256&e.subtreeFlags)for(e=e.child;null!==e;)Rs(e),e=e.sibling}function Rs(e){switch(e.tag){case 0:case 11:case 15:Ts(e),2048&e.flags&&Ui(9,e,e.return);break;case 3:case 12:default:Ts(e);break;case 22:var t=e.stateNode;null!==e.memoizedState&&4&t._visibility&&(null===e.return||13!==e.return.tag)?(t._visibility&=-5,As(e)):Ts(e)}}function As(e){var t=e.deletions;if(0!==(16&e.flags)){if(null!==t)for(var n=0;n<t.length;n++){var r=t[n];ns=r,zs(r,e)}_s(e)}for(e=e.child;null!==e;){switch((t=e).tag){case 0:case 11:case 15:Ui(8,t,t.return),As(t);break;case 22:4&(n=t.stateNode)._visibility&&(n._visibility&=-5,As(t));break;default:As(t)}e=e.sibling}}function zs(e,t){for(;null!==ns;){var n=ns;switch(n.tag){case 0:case 11:case 15:Ui(8,n,t);break;case 23:case 22:if(null!==n.memoizedState&&null!==n.memoizedState.cachePool){var r=n.memoizedState.cachePool.pool;null!=r&&r.refCount++}break;case 24:Wo(n.memoizedState.cache)}if(null!==(r=n.child))r.return=n,ns=r;else e:for(n=e;null!==ns;){var o=(r=ns).sibling,a=r.return;if(as(r),r===n){ns=null;break e}if(null!==o){o.return=a,ns=o;break e}ns=a}}}function Os(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Ls(e,t,n,r){return new Os(e,t,n,r)}function Ms(e){return!(!(e=e.prototype)||!e.isReactComponent)}function Ds(e,t){var n=e.alternate;return null===n?((n=Ls(e.tag,t,e.key,e.mode)).elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=31457280&e.flags,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n.refCleanup=e.refCleanup,n}function Is(e,t){e.flags&=31457282;var n=e.alternate;return null===n?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=n.childLanes,e.lanes=n.lanes,e.child=n.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=n.memoizedProps,e.memoizedState=n.memoizedState,e.updateQueue=n.updateQueue,e.type=n.type,t=n.dependencies,e.dependencies=null===t?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Fs(e,t,n,r,o,a){var i=0;if(r=e,"function"===typeof e)Ms(e)&&(i=1);else if("string"===typeof e)i=function(e,t,n){if(1===n||null!=t.itemProp)return!1;switch(e){case"meta":case"title":return!0;case"style":if("string"!==typeof t.precedence||"string"!==typeof t.href||""===t.href)break;return!0;case"link":if("string"!==typeof t.rel||"string"!==typeof t.href||""===t.href||t.onLoad||t.onError)break;return"stylesheet"!==t.rel||(e=t.disabled,"string"===typeof t.precedence&&null==e);case"script":if(t.async&&"function"!==typeof t.async&&"symbol"!==typeof t.async&&!t.onLoad&&!t.onError&&t.src&&"string"===typeof t.src)return!0}return!1}(e,n,G.current)?26:"html"===e||"head"===e||"body"===e?27:5;else e:switch(e){case d:return Hs(n.children,o,a,t);case f:i=8,o|=24;break;case p:return(e=Ls(12,n,t,2|o)).elementType=p,e.lanes=a,e;case y:return(e=Ls(13,n,t,o)).elementType=y,e.lanes=a,e;case b:return(e=Ls(19,n,t,o)).elementType=b,e.lanes=a,e;case k:return Us(n,o,a,t);default:if("object"===typeof e&&null!==e)switch(e.$$typeof){case m:case g:i=10;break e;case h:i=9;break e;case v:i=11;break e;case w:i=14;break e;case x:i=16,r=null;break e}i=29,n=Error(l(130,null===e?"null":typeof e,"")),r=null}return(t=Ls(i,n,t,o)).elementType=e,t.type=r,t.lanes=a,t}function Hs(e,t,n,r){return(e=Ls(7,e,r,t)).lanes=n,e}function Us(e,t,n,r){(e=Ls(22,e,r,t)).elementType=k,e.lanes=n;var o={_visibility:1,_pendingVisibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null,_current:null,detach:function(){var e=o._current;if(null===e)throw Error(l(456));if(0===(2&o._pendingVisibility)){var t=_r(e,2);null!==t&&(o._pendingVisibility|=2,Ac(t,e,2))}},attach:function(){var e=o._current;if(null===e)throw Error(l(456));if(0!==(2&o._pendingVisibility)){var t=_r(e,2);null!==t&&(o._pendingVisibility&=-3,Ac(t,e,2))}}};return e.stateNode=o,e}function Ws(e,t,n){return(e=Ls(6,e,null,t)).lanes=n,e}function Vs(e,t,n){return(t=Ls(4,null!==e.children?e.children:[],e.key,t)).lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Bs(e){e.flags|=4}function $s(e,t){if("stylesheet"!==t.type||0!==(4&t.state.loading))e.flags&=-16777217;else if(e.flags|=16777216,!_d(t)){if(null!==(t=_o.current)&&((4194176&rc)===rc?null!==To:(62914560&rc)!==rc&&0===(536870912&rc)||t!==To))throw po=so,io;e.flags|=8192}}function qs(e,t){null!==t&&(e.flags|=4),16384&e.flags&&(t=22!==e.tag?Te():536870912,e.lanes|=t,hc|=t)}function Ks(e,t){if(!Yr)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;null!==t;)null!==t.alternate&&(n=t),t=t.sibling;null===n?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;null!==n;)null!==n.alternate&&(r=n),n=n.sibling;null===r?t||null===e.tail?e.tail=null:e.tail.sibling=null:r.sibling=null}}function Qs(e){var t=null!==e.alternate&&e.alternate.child===e.child,n=0,r=0;if(t)for(var o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=31457280&o.subtreeFlags,r|=31457280&o.flags,o.return=e,o=o.sibling;else for(o=e.child;null!==o;)n|=o.lanes|o.childLanes,r|=o.subtreeFlags,r|=o.flags,o.return=e,o=o.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function Gs(e,t,n){var r=t.pendingProps;switch(Kr(t),t.tag){case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:case 1:return Qs(t),null;case 3:return n=t.stateNode,r=null,null!==e&&(r=e.memoizedState.cache),t.memoizedState.cache!==r&&(t.flags|=2048),bi(Ho),ee(),n.pendingContext&&(n.context=n.pendingContext,n.pendingContext=null),null!==e&&null!==e.child||(ro(t)?Bs(t):null===e||e.memoizedState.isDehydrated&&0===(256&t.flags)||(t.flags|=1024,null!==Xr&&(Oc(Xr),Xr=null))),Qs(t),null;case 26:return n=t.memoizedState,null===e?(Bs(t),null!==n?(Qs(t),$s(t,n)):(Qs(t),t.flags&=-16777217)):n?n!==e.memoizedState?(Bs(t),Qs(t),$s(t,n)):(Qs(t),t.flags&=-16777217):(e.memoizedProps!==r&&Bs(t),Qs(t),t.flags&=-16777217),null;case 27:ne(t),n=X.current;var o=t.type;if(null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Bs(t);else{if(!r){if(null===t.stateNode)throw Error(l(166));return Qs(t),null}e=G.current,ro(t)?to(t):(e=cd(o,r,n),t.stateNode=e,Bs(t))}return Qs(t),null;case 5:if(ne(t),n=t.type,null!==e&&null!=t.stateNode)e.memoizedProps!==r&&Bs(t);else{if(!r){if(null===t.stateNode)throw Error(l(166));return Qs(t),null}if(e=G.current,ro(t))to(t);else{switch(o=Gu(X.current),e){case 1:e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case 2:e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;default:switch(n){case"svg":e=o.createElementNS("http://www.w3.org/2000/svg",n);break;case"math":e=o.createElementNS("http://www.w3.org/1998/Math/MathML",n);break;case"script":(e=o.createElement("div")).innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e="string"===typeof r.is?o.createElement("select",{is:r.is}):o.createElement("select"),r.multiple?e.multiple=!0:r.size&&(e.size=r.size);break;default:e="string"===typeof r.is?o.createElement(n,{is:r.is}):o.createElement(n)}}e[Ie]=t,e[Fe]=r;e:for(o=t.child;null!==o;){if(5===o.tag||6===o.tag)e.appendChild(o.stateNode);else if(4!==o.tag&&27!==o.tag&&null!==o.child){o.child.return=o,o=o.child;continue}if(o===t)break e;for(;null===o.sibling;){if(null===o.return||o.return===t)break e;o=o.return}o.sibling.return=o.return,o=o.sibling}t.stateNode=e;e:switch(qu(e,n,r),n){case"button":case"input":case"select":case"textarea":e=!!r.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Bs(t)}}return Qs(t),t.flags&=-16777217,null;case 6:if(e&&null!=t.stateNode)e.memoizedProps!==r&&Bs(t);else{if("string"!==typeof r&&null===t.stateNode)throw Error(l(166));if(e=X.current,ro(t)){if(e=t.stateNode,n=t.memoizedProps,r=null,null!==(o=Qr))switch(o.tag){case 27:case 5:r=o.memoizedProps}e[Ie]=t,(e=!!(e.nodeValue===n||null!==r&&!0===r.suppressHydrationWarning||Wu(e.nodeValue,n)))||eo(t)}else(e=Gu(e).createTextNode(r))[Ie]=t,t.stateNode=e}return Qs(t),null;case 13:if(r=t.memoizedState,null===e||null!==e.memoizedState&&null!==e.memoizedState.dehydrated){if(o=ro(t),null!==r&&null!==r.dehydrated){if(null===e){if(!o)throw Error(l(318));if(!(o=null!==(o=t.memoizedState)?o.dehydrated:null))throw Error(l(317));o[Ie]=t}else oo(),0===(128&t.flags)&&(t.memoizedState=null),t.flags|=4;Qs(t),o=!1}else null!==Xr&&(Oc(Xr),Xr=null),o=!0;if(!o)return 256&t.flags?(Oo(t),t):(Oo(t),null)}if(Oo(t),0!==(128&t.flags))return t.lanes=n,t;if(n=null!==r,e=null!==e&&null!==e.memoizedState,n){o=null,null!==(r=t.child).alternate&&null!==r.alternate.memoizedState&&null!==r.alternate.memoizedState.cachePool&&(o=r.alternate.memoizedState.cachePool.pool);var a=null;null!==r.memoizedState&&null!==r.memoizedState.cachePool&&(a=r.memoizedState.cachePool.pool),a!==o&&(r.flags|=2048)}return n!==e&&n&&(t.child.flags|=8192),qs(t,t.updateQueue),Qs(t),null;case 4:return ee(),null===e&&Au(t.stateNode.containerInfo),Qs(t),null;case 10:return bi(t.type),Qs(t),null;case 19:if(K(Lo),null===(o=t.memoizedState))return Qs(t),null;if(r=0!==(128&t.flags),null===(a=o.rendering))if(r)Ks(o,!1);else{if(0!==uc||null!==e&&0!==(128&e.flags))for(e=t.child;null!==e;){if(null!==(a=Mo(e))){for(t.flags|=128,Ks(o,!1),e=a.updateQueue,t.updateQueue=e,qs(t,e),t.subtreeFlags=0,e=n,n=t.child;null!==n;)Is(n,e),n=n.sibling;return Q(Lo,1&Lo.current|2),t.child}e=e.sibling}null!==o.tail&&se()>wc&&(t.flags|=128,r=!0,Ks(o,!1),t.lanes=4194304)}else{if(!r)if(null!==(e=Mo(a))){if(t.flags|=128,r=!0,e=e.updateQueue,t.updateQueue=e,qs(t,e),Ks(o,!0),null===o.tail&&"hidden"===o.tailMode&&!a.alternate&&!Yr)return Qs(t),null}else 2*se()-o.renderingStartTime>wc&&536870912!==n&&(t.flags|=128,r=!0,Ks(o,!1),t.lanes=4194304);o.isBackwards?(a.sibling=t.child,t.child=a):(null!==(e=o.last)?e.sibling=a:t.child=a,o.last=a)}return null!==o.tail?(t=o.tail,o.rendering=t,o.tail=t.sibling,o.renderingStartTime=se(),t.sibling=null,e=Lo.current,Q(Lo,r?1&e|2:1&e),t):(Qs(t),null);case 22:case 23:return Oo(t),Po(),r=null!==t.memoizedState,null!==e?null!==e.memoizedState!==r&&(t.flags|=8192):r&&(t.flags|=8192),r?0!==(536870912&n)&&0===(128&t.flags)&&(Qs(t),6&t.subtreeFlags&&(t.flags|=8192)):Qs(t),null!==(n=t.updateQueue)&&qs(t,n.retryQueue),n=null,null!==e&&null!==e.memoizedState&&null!==e.memoizedState.cachePool&&(n=e.memoizedState.cachePool.pool),r=null,null!==t.memoizedState&&null!==t.memoizedState.cachePool&&(r=t.memoizedState.cachePool.pool),r!==n&&(t.flags|=2048),null!==e&&K(Go),null;case 24:return n=null,null!==e&&(n=e.memoizedState.cache),t.memoizedState.cache!==n&&(t.flags|=2048),bi(Ho),Qs(t),null;case 25:return null}throw Error(l(156,t.tag))}function Ys(e,t){switch(Kr(t),t.tag){case 1:return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 3:return bi(Ho),ee(),0!==(65536&(e=t.flags))&&0===(128&e)?(t.flags=-65537&e|128,t):null;case 26:case 27:case 5:return ne(t),null;case 13:if(Oo(t),null!==(e=t.memoizedState)&&null!==e.dehydrated){if(null===t.alternate)throw Error(l(340));oo()}return 65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 19:return K(Lo),null;case 4:return ee(),null;case 10:return bi(t.type),null;case 22:case 23:return Oo(t),Po(),null!==e&&K(Go),65536&(e=t.flags)?(t.flags=-65537&e|128,t):null;case 24:return bi(Ho),null;default:return null}}function Xs(e,t){switch(Kr(t),t.tag){case 3:bi(Ho),ee();break;case 26:case 27:case 5:ne(t);break;case 4:ee();break;case 13:Oo(t);break;case 19:K(Lo);break;case 10:bi(t.type);break;case 22:case 23:Oo(t),Po(),null!==e&&K(Go);break;case 24:bi(Ho)}}var Zs={getCacheForType:function(e){var t=Ei(Ho),n=t.data.get(e);return void 0===n&&(n=e(),t.data.set(e,n)),n}},Js="function"===typeof WeakMap?WeakMap:Map,ec=0,tc=null,nc=null,rc=0,oc=0,ac=null,lc=!1,ic=!1,sc=!1,cc=0,uc=0,dc=0,fc=0,pc=0,mc=0,hc=0,gc=null,vc=null,yc=!1,bc=0,wc=1/0,xc=null,kc=null,Sc=!1,Nc=null,Ec=0,Cc=0,jc=null,Pc=0,_c=null;function Tc(){if(0!==(2&ec)&&0!==rc)return rc&-rc;if(null!==T.T){return 0!==$o?$o:xu()}return Me()}function Rc(){0===mc&&(mc=0===(536870912&rc)||Yr?_e():536870912);var e=_o.current;return null!==e&&(e.flags|=32),mc}function Ac(e,t,n){(e===tc&&2===oc||null!==e.cancelPendingCommit)&&(Hc(e,0),Dc(e,rc,mc,!1)),Ae(e,n),0!==(2&ec)&&e===tc||(e===tc&&(0===(2&ec)&&(fc|=n),4===uc&&Dc(e,rc,mc,!1)),hu(e))}function zc(e,t,n){if(0!==(6&ec))throw Error(l(327));for(var r=!n&&0===(60&t)&&0===(t&e.expiredLanes)||je(e,t),o=r?function(e,t){var n=ec;ec|=2;var r=Wc(),o=Vc();tc!==e||rc!==t?(xc=null,wc=se()+500,Hc(e,t)):ic=je(e,t);e:for(;;)try{if(0!==oc&&null!==nc){t=nc;var a=ac;t:switch(oc){case 1:oc=0,ac=null,Yc(e,t,a,1);break;case 2:if(co(a)){oc=0,ac=null,Gc(t);break}t=function(){2===oc&&tc===e&&(oc=7),hu(e)},a.then(t,t);break e;case 3:oc=7;break e;case 4:oc=5;break e;case 7:co(a)?(oc=0,ac=null,Gc(t)):(oc=0,ac=null,Yc(e,t,a,7));break;case 5:var i=null;switch(nc.tag){case 26:i=nc.memoizedState;case 5:case 27:var s=nc;if(!i||_d(i)){oc=0,ac=null;var c=s.sibling;if(null!==c)nc=c;else{var u=s.return;null!==u?(nc=u,Xc(u)):nc=null}break t}}oc=0,ac=null,Yc(e,t,a,5);break;case 6:oc=0,ac=null,Yc(e,t,a,6);break;case 8:Fc(),uc=6;break e;default:throw Error(l(462))}}Kc();break}catch(d){Uc(e,d)}return vi=gi=null,T.H=r,T.A=o,ec=n,null!==nc?0:(tc=null,rc=0,Cr(),uc)}(e,t):$c(e,t,!0),a=r;;){if(0===o){ic&&!r&&Dc(e,t,0,!1);break}if(6===o)Dc(e,t,0,!lc);else{if(n=e.current.alternate,a&&!Mc(n)){o=$c(e,t,!1),a=!1;continue}if(2===o){if(a=t,e.errorRecoveryDisabledLanes&a)var i=0;else i=0!==(i=-536870913&e.pendingLanes)?i:536870912&i?536870912:0;if(0!==i){t=i;e:{var s=e;o=gc;var c=s.current.memoizedState.isDehydrated;if(c&&(Hc(s,i).flags|=256),2!==(i=$c(s,i,!1))){if(sc&&!c){s.errorRecoveryDisabledLanes|=a,fc|=a,o=4;break e}a=vc,vc=o,null!==a&&Oc(a)}o=i}if(a=!1,2!==o)continue}}if(1===o){Hc(e,0),Dc(e,t,0,!0);break}e:{switch(r=e,o){case 0:case 1:throw Error(l(345));case 4:if((4194176&t)===t){Dc(r,t,mc,!lc);break e}break;case 2:vc=null;break;case 3:case 5:break;default:throw Error(l(329))}if(r.finishedWork=n,r.finishedLanes=t,(62914560&t)===t&&10<(a=bc+300-se())){if(Dc(r,t,mc,!lc),0!==Ce(r,0))break e;r.timeoutHandle=ed(Lc.bind(null,r,n,vc,xc,yc,t,mc,fc,hc,lc,2,-0,0),a)}else Lc(r,n,vc,xc,yc,t,mc,fc,hc,lc,0,-0,0)}}break}hu(e)}function Oc(e){null===vc?vc=e:vc.push.apply(vc,e)}function Lc(e,t,n,r,o,a,i,s,c,u,d,f,p){var m=t.subtreeFlags;if((8192&m||16785408===(16785408&m))&&(Td={stylesheets:null,count:0,unsuspend:Rd},Ps(t),null!==(t=function(){if(null===Td)throw Error(l(475));var e=Td;return e.stylesheets&&0===e.count&&Od(e,e.stylesheets),0<e.count?function(t){var n=setTimeout((function(){if(e.stylesheets&&Od(e,e.stylesheets),e.unsuspend){var t=e.unsuspend;e.unsuspend=null,t()}}),6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(n)}}:null}())))return e.cancelPendingCommit=t(Jc.bind(null,e,n,r,o,i,s,c,1,f,p)),void Dc(e,a,i,!u);Jc(e,n,r,o,i,s,c,d,f,p)}function Mc(e){for(var t=e;;){var n=t.tag;if((0===n||11===n||15===n)&&16384&t.flags&&(null!==(n=t.updateQueue)&&null!==(n=n.stores)))for(var r=0;r<n.length;r++){var o=n[r],a=o.getSnapshot;o=o.value;try{if(!Qn(a(),o))return!1}catch(l){return!1}}if(n=t.child,16384&t.subtreeFlags&&null!==n)n.return=t,t=n;else{if(t===e)break;for(;null===t.sibling;){if(null===t.return||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Dc(e,t,n,r){t&=~pc,t&=~fc,e.suspendedLanes|=t,e.pingedLanes&=~t,r&&(e.warmLanes|=t),r=e.expirationTimes;for(var o=t;0<o;){var a=31-we(o),l=1<<a;r[a]=-1,o&=~l}0!==n&&ze(e,n,t)}function Ic(){return 0!==(6&ec)||(gu(0,!1),!1)}function Fc(){if(null!==nc){if(0===oc)var e=nc.return;else vi=gi=null,ya(e=nc),ho=null,go=0,e=nc;for(;null!==e;)Xs(e.alternate,e),e=e.return;nc=null}}function Hc(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;-1!==n&&(e.timeoutHandle=-1,td(n)),null!==(n=e.cancelPendingCommit)&&(e.cancelPendingCommit=null,n()),Fc(),tc=e,nc=n=Ds(e.current,null),rc=t,oc=0,ac=null,lc=!1,ic=je(e,t),sc=!1,hc=mc=pc=fc=dc=uc=0,vc=gc=null,yc=!1,0!==(8&t)&&(t|=32&t);var r=e.entangledLanes;if(0!==r)for(e=e.entanglements,r&=t;0<r;){var o=31-we(r),a=1<<o;t|=e[o],r&=~a}return cc=t,Cr(),n}function Uc(e,t){ea=null,T.H=El,t===lo?(t=mo(),oc=3):t===io?(t=mo(),oc=4):oc=t===Vl?8:null!==t&&"object"===typeof t&&"function"===typeof t.then?6:1,ac=t,null===nc&&(uc=1,Il(e,Or(t,e.current)))}function Wc(){var e=T.H;return T.H=El,null===e?El:e}function Vc(){var e=T.A;return T.A=Zs,e}function Bc(){uc=4,lc||(4194176&rc)!==rc&&null!==_o.current||(ic=!0),0===(134217727&dc)&&0===(134217727&fc)||null===tc||Dc(tc,rc,mc,!1)}function $c(e,t,n){var r=ec;ec|=2;var o=Wc(),a=Vc();tc===e&&rc===t||(xc=null,Hc(e,t)),t=!1;var l=uc;e:for(;;)try{if(0!==oc&&null!==nc){var i=nc,s=ac;switch(oc){case 8:Fc(),l=6;break e;case 3:case 2:case 6:null===_o.current&&(t=!0);var c=oc;if(oc=0,ac=null,Yc(e,i,s,c),n&&ic){l=0;break e}break;default:c=oc,oc=0,ac=null,Yc(e,i,s,c)}}qc(),l=uc;break}catch(u){Uc(e,u)}return t&&e.shellSuspendCounter++,vi=gi=null,ec=r,T.H=o,T.A=a,null===nc&&(tc=null,rc=0,Cr()),l}function qc(){for(;null!==nc;)Qc(nc)}function Kc(){for(;null!==nc&&!le();)Qc(nc)}function Qc(e){var t=mi(e.alternate,e,cc);e.memoizedProps=e.pendingProps,null===t?Xc(e):nc=t}function Gc(e){var t=e,n=t.alternate;switch(t.tag){case 15:case 0:t=Jl(n,t,t.pendingProps,t.type,void 0,rc);break;case 11:t=Jl(n,t,t.pendingProps,t.type.render,t.ref,rc);break;case 5:ya(t);default:Xs(n,t),t=mi(n,t=nc=Is(t,cc),cc)}e.memoizedProps=e.pendingProps,null===t?Xc(e):nc=t}function Yc(e,t,n,r){vi=gi=null,ya(t),ho=null,go=0;var o=t.return;try{if(function(e,t,n,r,o){if(n.flags|=32768,null!==r&&"object"===typeof r&&"function"===typeof r.then){if(null!==(t=n.alternate)&&ki(t,n,o,!0),null!==(n=_o.current)){switch(n.tag){case 13:return null===To?Bc():null===n.alternate&&0===uc&&(uc=3),n.flags&=-257,n.flags|=65536,n.lanes=o,r===so?n.flags|=16384:(null===(t=n.updateQueue)?n.updateQueue=new Set([r]):t.add(r),ou(e,r,o)),!1;case 22:return n.flags|=65536,r===so?n.flags|=16384:(null===(t=n.updateQueue)?(t={transitions:null,markerInstances:null,retryQueue:new Set([r])},n.updateQueue=t):null===(n=t.retryQueue)?t.retryQueue=new Set([r]):n.add(r),ou(e,r,o)),!1}throw Error(l(435,n.tag))}return ou(e,r,o),Bc(),!1}if(Yr)return null!==(t=_o.current)?(0===(65536&t.flags)&&(t.flags|=256),t.flags|=65536,t.lanes=o,r!==Jr&&ao(Or(e=Error(l(422),{cause:r}),n))):(r!==Jr&&ao(Or(t=Error(l(423),{cause:r}),n)),(e=e.current.alternate).flags|=65536,o&=-o,e.lanes|=o,r=Or(r,n),Oi(e,o=Hl(e.stateNode,r,o)),4!==uc&&(uc=2)),!1;var a=Error(l(520),{cause:r});if(a=Or(a,n),null===gc?gc=[a]:gc.push(a),4!==uc&&(uc=2),null===t)return!0;r=Or(r,n),n=t;do{switch(n.tag){case 3:return n.flags|=65536,e=o&-o,n.lanes|=e,Oi(n,e=Hl(n.stateNode,r,e)),!1;case 1:if(t=n.type,a=n.stateNode,0===(128&n.flags)&&("function"===typeof t.getDerivedStateFromError||null!==a&&"function"===typeof a.componentDidCatch&&(null===kc||!kc.has(a))))return n.flags|=65536,o&=-o,n.lanes|=o,Wl(o=Ul(o),e,n,r),Oi(n,o),!1}n=n.return}while(null!==n);return!1}(e,o,t,n,rc))return uc=1,Il(e,Or(n,e.current)),void(nc=null)}catch(a){if(null!==o)throw nc=o,a;return uc=1,Il(e,Or(n,e.current)),void(nc=null)}32768&t.flags?(Yr||1===r?e=!0:ic||0!==(536870912&rc)?e=!1:(lc=e=!0,(2===r||3===r||6===r)&&(null!==(r=_o.current)&&13===r.tag&&(r.flags|=16384))),Zc(t,e)):Xc(t)}function Xc(e){var t=e;do{if(0!==(32768&t.flags))return void Zc(t,lc);e=t.return;var n=Gs(t.alternate,t,cc);if(null!==n)return void(nc=n);if(null!==(t=t.sibling))return void(nc=t);nc=t=e}while(null!==t);0===uc&&(uc=5)}function Zc(e,t){do{var n=Ys(e.alternate,e);if(null!==n)return n.flags&=32767,void(nc=n);if(null!==(n=e.return)&&(n.flags|=32768,n.subtreeFlags=0,n.deletions=null),!t&&null!==(e=e.sibling))return void(nc=e);nc=e=n}while(null!==e);uc=6,nc=null}function Jc(e,t,n,r,o,a,i,s,c,u){var d=T.T,f=W.p;try{W.p=2,T.T=null,function(e,t,n,r,o,a,i,s){do{tu()}while(null!==Nc);if(0!==(6&ec))throw Error(l(327));var c=e.finishedWork;if(r=e.finishedLanes,null===c)return null;if(e.finishedWork=null,e.finishedLanes=0,c===e.current)throw Error(l(177));e.callbackNode=null,e.callbackPriority=0,e.cancelPendingCommit=null;var u=c.lanes|c.childLanes;if(function(e,t,n,r,o,a){var l=e.pendingLanes;e.pendingLanes=n,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=n,e.entangledLanes&=n,e.errorRecoveryDisabledLanes&=n,e.shellSuspendCounter=0;var i=e.entanglements,s=e.expirationTimes,c=e.hiddenUpdates;for(n=l&~n;0<n;){var u=31-we(n),d=1<<u;i[u]=0,s[u]=-1;var f=c[u];if(null!==f)for(c[u]=null,u=0;u<f.length;u++){var p=f[u];null!==p&&(p.lane&=-536870913)}n&=~d}0!==r&&ze(e,r,0),0!==a&&0===o&&0!==e.tag&&(e.suspendedLanes|=a&~(l&~t))}(e,r,u|=Er,a,i,s),e===tc&&(nc=tc=null,rc=0),0===(10256&c.subtreeFlags)&&0===(10256&c.flags)||Sc||(Sc=!0,Cc=u,jc=n,function(e,t){oe(e,t)}(fe,(function(){return tu(),null}))),n=0!==(15990&c.flags),0!==(15990&c.subtreeFlags)||n?(n=T.T,T.T=null,a=W.p,W.p=2,i=ec,ec|=4,function(e,t){if(e=e.containerInfo,Ku=Bd,er(e=Jn(e))){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{var r=(n=(n=e.ownerDocument)&&n.defaultView||window).getSelection&&n.getSelection();if(r&&0!==r.rangeCount){n=r.anchorNode;var o=r.anchorOffset,a=r.focusNode;r=r.focusOffset;try{n.nodeType,a.nodeType}catch(g){n=null;break e}var i=0,s=-1,c=-1,u=0,d=0,f=e,p=null;t:for(;;){for(var m;f!==n||0!==o&&3!==f.nodeType||(s=i+o),f!==a||0!==r&&3!==f.nodeType||(c=i+r),3===f.nodeType&&(i+=f.nodeValue.length),null!==(m=f.firstChild);)p=f,f=m;for(;;){if(f===e)break t;if(p===n&&++u===o&&(s=i),p===a&&++d===r&&(c=i),null!==(m=f.nextSibling))break;p=(f=p).parentNode}f=m}n=-1===s||-1===c?null:{start:s,end:c}}else n=null}n=n||{start:0,end:0}}else n=null;for(Qu={focusedElem:e,selectionRange:n},Bd=!1,ns=t;null!==ns;)if(e=(t=ns).child,0!==(1028&t.subtreeFlags)&&null!==e)e.return=t,ns=e;else for(;null!==ns;){switch(a=(t=ns).alternate,e=t.flags,t.tag){case 0:case 11:case 15:case 5:case 26:case 27:case 6:case 4:case 17:break;case 1:if(0!==(1024&e)&&null!==a){e=void 0,n=t,o=a.memoizedProps,a=a.memoizedState,r=n.stateNode;try{var h=zl(n.type,o,(n.elementType,n.type));e=r.getSnapshotBeforeUpdate(h,a),r.__reactInternalSnapshotBeforeUpdate=e}catch(v){ru(n,n.return,v)}}break;case 3:if(0!==(1024&e))if(9===(n=(e=t.stateNode.containerInfo).nodeType))ld(e);else if(1===n)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":ld(e);break;default:e.textContent=""}break;default:if(0!==(1024&e))throw Error(l(163))}if(null!==(e=t.sibling)){e.return=t.return,ns=e;break}ns=t.return}h=rs,rs=!1}(e,c),ms(c,e),tr(Qu,e.containerInfo),Bd=!!Ku,Qu=Ku=null,e.current=c,os(e,c.alternate,c),ie(),ec=i,W.p=a,T.T=n):e.current=c,Sc?(Sc=!1,Nc=e,Ec=r):eu(e,u),u=e.pendingLanes,0===u&&(kc=null),function(e){if(ye&&"function"===typeof ye.onCommitFiberRoot)try{ye.onCommitFiberRoot(ve,e,void 0,128===(128&e.current.flags))}catch(t){}}(c.stateNode),hu(e),null!==t)for(o=e.onRecoverableError,c=0;c<t.length;c++)u=t[c],o(u.value,{componentStack:u.stack});0!==(3&Ec)&&tu(),u=e.pendingLanes,0!==(4194218&r)&&0!==(42&u)?e===_c?Pc++:(Pc=0,_c=e):Pc=0,gu(0,!1)}(e,t,n,r,f,o,a,i)}finally{T.T=d,W.p=f}}function eu(e,t){0===(e.pooledCacheLanes&=t)&&(null!=(t=e.pooledCache)&&(e.pooledCache=null,Wo(t)))}function tu(){if(null!==Nc){var e=Nc,t=Cc;Cc=0;var n=Le(Ec),r=T.T,o=W.p;try{if(W.p=32>n?32:n,T.T=null,null===Nc)var a=!1;else{n=jc,jc=null;var i=Nc,s=Ec;if(Nc=null,Ec=0,0!==(6&ec))throw Error(l(331));var c=ec;if(ec|=4,Rs(i.current),Ss(i,i.current,s,n),ec=c,gu(0,!1),ye&&"function"===typeof ye.onPostCommitFiberRoot)try{ye.onPostCommitFiberRoot(ve,i)}catch(u){}a=!0}return a}finally{W.p=o,T.T=r,eu(e,t)}}return!1}function nu(e,t,n){t=Or(n,t),null!==(e=Ai(e,t=Hl(e.stateNode,t,2),2))&&(Ae(e,2),hu(e))}function ru(e,t,n){if(3===e.tag)nu(e,e,n);else for(;null!==t;){if(3===t.tag){nu(t,e,n);break}if(1===t.tag){var r=t.stateNode;if("function"===typeof t.type.getDerivedStateFromError||"function"===typeof r.componentDidCatch&&(null===kc||!kc.has(r))){e=Or(n,e),null!==(r=Ai(t,n=Ul(2),2))&&(Wl(n,r,t,e),Ae(r,2),hu(r));break}}t=t.return}}function ou(e,t,n){var r=e.pingCache;if(null===r){r=e.pingCache=new Js;var o=new Set;r.set(t,o)}else void 0===(o=r.get(t))&&(o=new Set,r.set(t,o));o.has(n)||(sc=!0,o.add(n),e=au.bind(null,e,t,n),t.then(e,e))}function au(e,t,n){var r=e.pingCache;null!==r&&r.delete(t),e.pingedLanes|=e.suspendedLanes&n,e.warmLanes&=~n,tc===e&&(rc&n)===n&&(4===uc||3===uc&&(62914560&rc)===rc&&300>se()-bc?0===(2&ec)&&Hc(e,0):pc|=n,hc===rc&&(hc=0)),hu(e)}function lu(e,t){0===t&&(t=Te()),null!==(e=_r(e,t))&&(Ae(e,t),hu(e))}function iu(e){var t=e.memoizedState,n=0;null!==t&&(n=t.retryLane),lu(e,n)}function su(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,o=e.memoizedState;null!==o&&(n=o.retryLane);break;case 19:r=e.stateNode;break;case 22:r=e.stateNode._retryCache;break;default:throw Error(l(314))}null!==r&&r.delete(t),lu(e,n)}var cu=null,uu=null,du=!1,fu=!1,pu=!1,mu=0;function hu(e){var t;e!==uu&&null===e.next&&(null===uu?cu=uu=e:uu=uu.next=e),fu=!0,du||(du=!0,t=vu,rd((function(){0!==(6&ec)?oe(ue,t):t()})))}function gu(e,t){if(!pu&&fu){pu=!0;do{for(var n=!1,r=cu;null!==r;){if(!t)if(0!==e){var o=r.pendingLanes;if(0===o)var a=0;else{var l=r.suspendedLanes,i=r.pingedLanes;a=(1<<31-we(42|e)+1)-1,a=201326677&(a&=o&~(l&~i))?201326677&a|1:a?2|a:0}0!==a&&(n=!0,wu(r,a))}else a=rc,0===(3&(a=Ce(r,r===tc?a:0)))||je(r,a)||(n=!0,wu(r,a));r=r.next}}while(n);pu=!1}}function vu(){fu=du=!1;var e=0;0!==mu&&(function(){var e=window.event;if(e&&"popstate"===e.type)return e!==Ju&&(Ju=e,!0);return Ju=null,!1}()&&(e=mu),mu=0);for(var t=se(),n=null,r=cu;null!==r;){var o=r.next,a=yu(r,t);0===a?(r.next=null,null===n?cu=o:n.next=o,null===o&&(uu=n)):(n=r,(0!==e||0!==(3&a))&&(fu=!0)),r=o}gu(e,!1)}function yu(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,o=e.expirationTimes,a=-62914561&e.pendingLanes;0<a;){var l=31-we(a),i=1<<l,s=o[l];-1===s?0!==(i&n)&&0===(i&r)||(o[l]=Pe(i,t)):s<=t&&(e.expiredLanes|=i),a&=~i}if(n=rc,n=Ce(e,e===(t=tc)?n:0),r=e.callbackNode,0===n||e===t&&2===oc||null!==e.cancelPendingCommit)return null!==r&&null!==r&&ae(r),e.callbackNode=null,e.callbackPriority=0;if(0===(3&n)||je(e,n)){if((t=n&-n)===e.callbackPriority)return t;switch(null!==r&&ae(r),Le(n)){case 2:case 8:n=de;break;case 32:default:n=fe;break;case 268435456:n=me}return r=bu.bind(null,e),n=oe(n,r),e.callbackPriority=t,e.callbackNode=n,t}return null!==r&&null!==r&&ae(r),e.callbackPriority=2,e.callbackNode=null,2}function bu(e,t){var n=e.callbackNode;if(tu()&&e.callbackNode!==n)return null;var r=rc;return 0===(r=Ce(e,e===tc?r:0))?null:(zc(e,r,t),yu(e,se()),null!=e.callbackNode&&e.callbackNode===n?bu.bind(null,e):null)}function wu(e,t){if(tu())return null;zc(e,t,!0)}function xu(){return 0===mu&&(mu=_e()),mu}function ku(e){return null==e||"symbol"===typeof e||"boolean"===typeof e?null:"function"===typeof e?e:_t(""+e)}function Su(e,t){var n=t.ownerDocument.createElement("input");return n.name=t.name,n.value=t.value,e.id&&n.setAttribute("form",e.id),t.parentNode.insertBefore(n,t),e=new FormData(e),n.parentNode.removeChild(n),e}for(var Nu=0;Nu<xr.length;Nu++){var Eu=xr[Nu];kr(Eu.toLowerCase(),"on"+(Eu[0].toUpperCase()+Eu.slice(1)))}kr(pr,"onAnimationEnd"),kr(mr,"onAnimationIteration"),kr(hr,"onAnimationStart"),kr("dblclick","onDoubleClick"),kr("focusin","onFocus"),kr("focusout","onBlur"),kr(gr,"onTransitionRun"),kr(vr,"onTransitionStart"),kr(yr,"onTransitionCancel"),kr(br,"onTransitionEnd"),tt("onMouseEnter",["mouseout","mouseover"]),tt("onMouseLeave",["mouseout","mouseover"]),tt("onPointerEnter",["pointerout","pointerover"]),tt("onPointerLeave",["pointerout","pointerover"]),et("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),et("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),et("onBeforeInput",["compositionend","keypress","textInput","paste"]),et("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),et("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),et("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Cu="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),ju=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Cu));function Pu(e,t){t=0!==(4&t);for(var n=0;n<e.length;n++){var r=e[n],o=r.event;r=r.listeners;e:{var a=void 0;if(t)for(var l=r.length-1;0<=l;l--){var i=r[l],s=i.instance,c=i.currentTarget;if(i=i.listener,s!==a&&o.isPropagationStopped())break e;a=i,o.currentTarget=c;try{a(o)}catch(u){Ol(u)}o.currentTarget=null,a=s}else for(l=0;l<r.length;l++){if(s=(i=r[l]).instance,c=i.currentTarget,i=i.listener,s!==a&&o.isPropagationStopped())break e;a=i,o.currentTarget=c;try{a(o)}catch(u){Ol(u)}o.currentTarget=null,a=s}}}}function _u(e,t){var n=t[Ue];void 0===n&&(n=t[Ue]=new Set);var r=e+"__bubble";n.has(r)||(zu(t,e,2,!1),n.add(r))}function Tu(e,t,n){var r=0;t&&(r|=4),zu(n,e,r,t)}var Ru="_reactListening"+Math.random().toString(36).slice(2);function Au(e){if(!e[Ru]){e[Ru]=!0,Ze.forEach((function(t){"selectionchange"!==t&&(ju.has(t)||Tu(t,!1,e),Tu(t,!0,e))}));var t=9===e.nodeType?e:e.ownerDocument;null===t||t[Ru]||(t[Ru]=!0,Tu("selectionchange",!1,t))}}function zu(e,t,n,r){switch(Xd(t)){case 2:var o=$d;break;case 8:o=qd;break;default:o=Kd}n=o.bind(null,t,n,e),o=void 0,!It||"touchstart"!==t&&"touchmove"!==t&&"wheel"!==t||(o=!0),r?void 0!==o?e.addEventListener(t,n,{capture:!0,passive:o}):e.addEventListener(t,n,!0):void 0!==o?e.addEventListener(t,n,{passive:o}):e.addEventListener(t,n,!1)}function Ou(e,t,n,r,o){var a=r;if(0===(1&t)&&0===(2&t)&&null!==r)e:for(;;){if(null===r)return;var l=r.tag;if(3===l||4===l){var i=r.stateNode.containerInfo;if(i===o||8===i.nodeType&&i.parentNode===o)break;if(4===l)for(l=r.return;null!==l;){var s=l.tag;if((3===s||4===s)&&((s=l.stateNode.containerInfo)===o||8===s.nodeType&&s.parentNode===o))return;l=l.return}for(;null!==i;){if(null===(l=Ke(i)))return;if(5===(s=l.tag)||6===s||26===s||27===s){r=a=l;continue e}i=i.parentNode}}r=r.return}Mt((function(){var r=a,o=Rt(n),l=[];e:{var i=wr.get(e);if(void 0!==i){var s=Zt,c=e;switch(e){case"keypress":if(0===Bt(n))break e;case"keydown":case"keyup":s=mn;break;case"focusin":c="focus",s=on;break;case"focusout":c="blur",s=on;break;case"beforeblur":case"afterblur":s=on;break;case"click":if(2===n.button)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":s=nn;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":s=rn;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":s=gn;break;case pr:case mr:case hr:s=an;break;case br:s=vn;break;case"scroll":case"scrollend":s=en;break;case"wheel":s=yn;break;case"copy":case"cut":case"paste":s=ln;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":s=hn;break;case"toggle":case"beforetoggle":s=bn}var u=0!==(4&t),d=!u&&("scroll"===e||"scrollend"===e),f=u?null!==i?i+"Capture":null:i;u=[];for(var p,m=r;null!==m;){var h=m;if(p=h.stateNode,5!==(h=h.tag)&&26!==h&&27!==h||null===p||null===f||null!=(h=Dt(m,f))&&u.push(Lu(m,h,p)),d)break;m=m.return}0<u.length&&(i=new s(i,c,null,n,o),l.push({event:i,listeners:u}))}}if(0===(7&t)){if(s="mouseout"===e||"pointerout"===e,(!(i="mouseover"===e||"pointerover"===e)||n===Tt||!(c=n.relatedTarget||n.fromElement)||!Ke(c)&&!c[He])&&(s||i)&&(i=o.window===o?o:(i=o.ownerDocument)?i.defaultView||i.parentWindow:window,s?(s=r,null!==(c=(c=n.relatedTarget||n.toElement)?Ke(c):null)&&(d=D(c),u=c.tag,c!==d||5!==u&&27!==u&&6!==u)&&(c=null)):(s=null,c=r),s!==c)){if(u=nn,h="onMouseLeave",f="onMouseEnter",m="mouse","pointerout"!==e&&"pointerover"!==e||(u=hn,h="onPointerLeave",f="onPointerEnter",m="pointer"),d=null==s?i:Ge(s),p=null==c?i:Ge(c),(i=new u(h,m+"leave",s,n,o)).target=d,i.relatedTarget=p,h=null,Ke(o)===r&&((u=new u(f,m+"enter",c,n,o)).target=p,u.relatedTarget=d,h=u),d=h,s&&c)e:{for(f=c,m=0,p=u=s;p;p=Du(p))m++;for(p=0,h=f;h;h=Du(h))p++;for(;0<m-p;)u=Du(u),m--;for(;0<p-m;)f=Du(f),p--;for(;m--;){if(u===f||null!==f&&u===f.alternate)break e;u=Du(u),f=Du(f)}u=null}else u=null;null!==s&&Iu(l,i,s,u,!1),null!==c&&null!==d&&Iu(l,d,c,u,!0)}if("select"===(s=(i=r?Ge(r):window).nodeName&&i.nodeName.toLowerCase())||"input"===s&&"file"===i.type)var g=Dn;else if(Rn(i))if(In)g=Kn;else{g=$n;var v=Bn}else!(s=i.nodeName)||"input"!==s.toLowerCase()||"checkbox"!==i.type&&"radio"!==i.type?r&&Ct(r.elementType)&&(g=Dn):g=qn;switch(g&&(g=g(e,r))?An(l,g,n,o):(v&&v(e,i,r),"focusout"===e&&r&&"number"===i.type&&null!=r.memoizedProps.value&&yt(i,"number",i.value)),v=r?Ge(r):window,e){case"focusin":(Rn(v)||"true"===v.contentEditable)&&(rr=v,or=r,ar=null);break;case"focusout":ar=or=rr=null;break;case"mousedown":lr=!0;break;case"contextmenu":case"mouseup":case"dragend":lr=!1,ir(l,n,o);break;case"selectionchange":if(nr)break;case"keydown":case"keyup":ir(l,n,o)}var y;if(xn)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else _n?jn(e,n)&&(b="onCompositionEnd"):"keydown"===e&&229===n.keyCode&&(b="onCompositionStart");b&&(Nn&&"ko"!==n.locale&&(_n||"onCompositionStart"!==b?"onCompositionEnd"===b&&_n&&(y=Vt()):(Ut="value"in(Ht=o)?Ht.value:Ht.textContent,_n=!0)),0<(v=Mu(r,b)).length&&(b=new sn(b,e,null,n,o),l.push({event:b,listeners:v}),y?b.data=y:null!==(y=Pn(n))&&(b.data=y))),(y=Sn?function(e,t){switch(e){case"compositionend":return Pn(t);case"keypress":return 32!==t.which?null:(Cn=!0,En);case"textInput":return(e=t.data)===En&&Cn?null:e;default:return null}}(e,n):function(e,t){if(_n)return"compositionend"===e||!xn&&jn(e,t)?(e=Vt(),Wt=Ut=Ht=null,_n=!1,e):null;switch(e){case"paste":default:return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Nn&&"ko"!==t.locale?null:t.data}}(e,n))&&(0<(b=Mu(r,"onBeforeInput")).length&&(v=new sn("onBeforeInput","beforeinput",null,n,o),l.push({event:v,listeners:b}),v.data=y)),function(e,t,n,r,o){if("submit"===t&&n&&n.stateNode===o){var a=ku((o[Fe]||null).action),l=r.submitter;l&&null!==(t=(t=l[Fe]||null)?ku(t.formAction):l.getAttribute("formAction"))&&(a=t,l=null);var i=new Zt("action","action",null,r,o);e.push({event:i,listeners:[{instance:null,listener:function(){if(r.defaultPrevented){if(0!==mu){var e=l?Su(o,l):new FormData(o);dl(n,{pending:!0,data:e,method:o.method,action:a},null,e)}}else"function"===typeof a&&(i.preventDefault(),e=l?Su(o,l):new FormData(o),dl(n,{pending:!0,data:e,method:o.method,action:a},a,e))},currentTarget:o}]})}}(l,e,r,n,o)}Pu(l,t)}))}function Lu(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Mu(e,t){for(var n=t+"Capture",r=[];null!==e;){var o=e,a=o.stateNode;5!==(o=o.tag)&&26!==o&&27!==o||null===a||(null!=(o=Dt(e,n))&&r.unshift(Lu(e,o,a)),null!=(o=Dt(e,t))&&r.push(Lu(e,o,a))),e=e.return}return r}function Du(e){if(null===e)return null;do{e=e.return}while(e&&5!==e.tag&&27!==e.tag);return e||null}function Iu(e,t,n,r,o){for(var a=t._reactName,l=[];null!==n&&n!==r;){var i=n,s=i.alternate,c=i.stateNode;if(i=i.tag,null!==s&&s===r)break;5!==i&&26!==i&&27!==i||null===c||(s=c,o?null!=(c=Dt(n,a))&&l.unshift(Lu(n,c,s)):o||null!=(c=Dt(n,a))&&l.push(Lu(n,c,s))),n=n.return}0!==l.length&&e.push({event:t,listeners:l})}var Fu=/\r\n?/g,Hu=/\u0000|\uFFFD/g;function Uu(e){return("string"===typeof e?e:""+e).replace(Fu,"\n").replace(Hu,"")}function Wu(e,t){return t=Uu(t),Uu(e)===t}function Vu(){}function Bu(e,t,n,r,o,a){switch(n){case"children":"string"===typeof r?"body"===t||"textarea"===t&&""===r||kt(e,r):("number"===typeof r||"bigint"===typeof r)&&"body"!==t&&kt(e,""+r);break;case"className":it(e,"class",r);break;case"tabIndex":it(e,"tabindex",r);break;case"dir":case"role":case"viewBox":case"width":case"height":it(e,n,r);break;case"style":Et(e,r,a);break;case"data":if("object"!==t){it(e,"data",r);break}case"src":case"href":if(""===r&&("a"!==t||"href"!==n)){e.removeAttribute(n);break}if(null==r||"function"===typeof r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"action":case"formAction":if("function"===typeof r){e.setAttribute(n,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}if("function"===typeof a&&("formAction"===n?("input"!==t&&Bu(e,t,"name",o.name,o,null),Bu(e,t,"formEncType",o.formEncType,o,null),Bu(e,t,"formMethod",o.formMethod,o,null),Bu(e,t,"formTarget",o.formTarget,o,null)):(Bu(e,t,"encType",o.encType,o,null),Bu(e,t,"method",o.method,o,null),Bu(e,t,"target",o.target,o,null))),null==r||"symbol"===typeof r||"boolean"===typeof r){e.removeAttribute(n);break}r=_t(""+r),e.setAttribute(n,r);break;case"onClick":null!=r&&(e.onclick=Vu);break;case"onScroll":null!=r&&_u("scroll",e);break;case"onScrollEnd":null!=r&&_u("scrollend",e);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(l(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(l(60));e.innerHTML=n}}break;case"multiple":e.multiple=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"muted":e.muted=r&&"function"!==typeof r&&"symbol"!==typeof r;break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":case"autoFocus":break;case"xlinkHref":if(null==r||"function"===typeof r||"boolean"===typeof r||"symbol"===typeof r){e.removeAttribute("xlink:href");break}n=_t(""+r),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",n);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""+r):e.removeAttribute(n);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,""):e.removeAttribute(n);break;case"capture":case"download":!0===r?e.setAttribute(n,""):!1!==r&&null!=r&&"function"!==typeof r&&"symbol"!==typeof r?e.setAttribute(n,r):e.removeAttribute(n);break;case"cols":case"rows":case"size":case"span":null!=r&&"function"!==typeof r&&"symbol"!==typeof r&&!isNaN(r)&&1<=r?e.setAttribute(n,r):e.removeAttribute(n);break;case"rowSpan":case"start":null==r||"function"===typeof r||"symbol"===typeof r||isNaN(r)?e.removeAttribute(n):e.setAttribute(n,r);break;case"popover":_u("beforetoggle",e),_u("toggle",e),lt(e,"popover",r);break;case"xlinkActuate":st(e,"http://www.w3.org/1999/xlink","xlink:actuate",r);break;case"xlinkArcrole":st(e,"http://www.w3.org/1999/xlink","xlink:arcrole",r);break;case"xlinkRole":st(e,"http://www.w3.org/1999/xlink","xlink:role",r);break;case"xlinkShow":st(e,"http://www.w3.org/1999/xlink","xlink:show",r);break;case"xlinkTitle":st(e,"http://www.w3.org/1999/xlink","xlink:title",r);break;case"xlinkType":st(e,"http://www.w3.org/1999/xlink","xlink:type",r);break;case"xmlBase":st(e,"http://www.w3.org/XML/1998/namespace","xml:base",r);break;case"xmlLang":st(e,"http://www.w3.org/XML/1998/namespace","xml:lang",r);break;case"xmlSpace":st(e,"http://www.w3.org/XML/1998/namespace","xml:space",r);break;case"is":lt(e,"is",r);break;case"innerText":case"textContent":break;default:(!(2<n.length)||"o"!==n[0]&&"O"!==n[0]||"n"!==n[1]&&"N"!==n[1])&&lt(e,n=jt.get(n)||n,r)}}function $u(e,t,n,r,o,a){switch(n){case"style":Et(e,r,a);break;case"dangerouslySetInnerHTML":if(null!=r){if("object"!==typeof r||!("__html"in r))throw Error(l(61));if(null!=(n=r.__html)){if(null!=o.children)throw Error(l(60));e.innerHTML=n}}break;case"children":"string"===typeof r?kt(e,r):("number"===typeof r||"bigint"===typeof r)&&kt(e,""+r);break;case"onScroll":null!=r&&_u("scroll",e);break;case"onScrollEnd":null!=r&&_u("scrollend",e);break;case"onClick":null!=r&&(e.onclick=Vu);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":case"innerText":case"textContent":break;default:Je.hasOwnProperty(n)||("o"!==n[0]||"n"!==n[1]||(o=n.endsWith("Capture"),t=n.slice(2,o?n.length-7:void 0),"function"===typeof(a=null!=(a=e[Fe]||null)?a[n]:null)&&e.removeEventListener(t,a,o),"function"!==typeof r)?n in e?e[n]=r:!0===r?e.setAttribute(n,""):lt(e,n,r):("function"!==typeof a&&null!==a&&(n in e?e[n]=null:e.hasAttribute(n)&&e.removeAttribute(n)),e.addEventListener(t,r,o)))}}function qu(e,t,n){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":_u("error",e),_u("load",e);var r,o=!1,a=!1;for(r in n)if(n.hasOwnProperty(r)){var i=n[r];if(null!=i)switch(r){case"src":o=!0;break;case"srcSet":a=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:Bu(e,t,r,i,n,null)}}return a&&Bu(e,t,"srcSet",n.srcSet,n,null),void(o&&Bu(e,t,"src",n.src,n,null));case"input":_u("invalid",e);var s=r=i=a=null,c=null,u=null;for(o in n)if(n.hasOwnProperty(o)){var d=n[o];if(null!=d)switch(o){case"name":a=d;break;case"type":i=d;break;case"checked":c=d;break;case"defaultChecked":u=d;break;case"value":r=d;break;case"defaultValue":s=d;break;case"children":case"dangerouslySetInnerHTML":if(null!=d)throw Error(l(137,t));break;default:Bu(e,t,o,d,n,null)}}return vt(e,r,s,c,u,i,a,!1),void dt(e);case"select":for(a in _u("invalid",e),o=i=r=null,n)if(n.hasOwnProperty(a)&&null!=(s=n[a]))switch(a){case"value":r=s;break;case"defaultValue":i=s;break;case"multiple":o=s;default:Bu(e,t,a,s,n,null)}return t=r,n=i,e.multiple=!!o,void(null!=t?bt(e,!!o,t,!1):null!=n&&bt(e,!!o,n,!0));case"textarea":for(i in _u("invalid",e),r=a=o=null,n)if(n.hasOwnProperty(i)&&null!=(s=n[i]))switch(i){case"value":o=s;break;case"defaultValue":a=s;break;case"children":r=s;break;case"dangerouslySetInnerHTML":if(null!=s)throw Error(l(91));break;default:Bu(e,t,i,s,n,null)}return xt(e,o,a,r),void dt(e);case"option":for(c in n)if(n.hasOwnProperty(c)&&null!=(o=n[c]))if("selected"===c)e.selected=o&&"function"!==typeof o&&"symbol"!==typeof o;else Bu(e,t,c,o,n,null);return;case"dialog":_u("cancel",e),_u("close",e);break;case"iframe":case"object":_u("load",e);break;case"video":case"audio":for(o=0;o<Cu.length;o++)_u(Cu[o],e);break;case"image":_u("error",e),_u("load",e);break;case"details":_u("toggle",e);break;case"embed":case"source":case"link":_u("error",e),_u("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(u in n)if(n.hasOwnProperty(u)&&null!=(o=n[u]))switch(u){case"children":case"dangerouslySetInnerHTML":throw Error(l(137,t));default:Bu(e,t,u,o,n,null)}return;default:if(Ct(t)){for(d in n)n.hasOwnProperty(d)&&(void 0!==(o=n[d])&&$u(e,t,d,o,n,void 0));return}}for(s in n)n.hasOwnProperty(s)&&(null!=(o=n[s])&&Bu(e,t,s,o,n,null))}var Ku=null,Qu=null;function Gu(e){return 9===e.nodeType?e:e.ownerDocument}function Yu(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Xu(e,t){if(0===e)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return 1===e&&"foreignObject"===t?0:e}function Zu(e,t){return"textarea"===e||"noscript"===e||"string"===typeof t.children||"number"===typeof t.children||"bigint"===typeof t.children||"object"===typeof t.dangerouslySetInnerHTML&&null!==t.dangerouslySetInnerHTML&&null!=t.dangerouslySetInnerHTML.__html}var Ju=null;var ed="function"===typeof setTimeout?setTimeout:void 0,td="function"===typeof clearTimeout?clearTimeout:void 0,nd="function"===typeof Promise?Promise:void 0,rd="function"===typeof queueMicrotask?queueMicrotask:"undefined"!==typeof nd?function(e){return nd.resolve(null).then(e).catch(od)}:ed;function od(e){setTimeout((function(){throw e}))}function ad(e,t){var n=t,r=0;do{var o=n.nextSibling;if(e.removeChild(n),o&&8===o.nodeType)if("/$"===(n=o.data)){if(0===r)return e.removeChild(o),void gf(t);r--}else"$"!==n&&"$?"!==n&&"$!"!==n||r++;n=o}while(n);gf(t)}function ld(e){var t=e.firstChild;for(t&&10===t.nodeType&&(t=t.nextSibling);t;){var n=t;switch(t=t.nextSibling,n.nodeName){case"HTML":case"HEAD":case"BODY":ld(n),qe(n);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if("stylesheet"===n.rel.toLowerCase())continue}e.removeChild(n)}}function id(e){for(;null!=e;e=e.nextSibling){var t=e.nodeType;if(1===t||3===t)break;if(8===t){if("$"===(t=e.data)||"$!"===t||"$?"===t||"F!"===t||"F"===t)break;if("/$"===t)return null}}return e}function sd(e){e=e.previousSibling;for(var t=0;e;){if(8===e.nodeType){var n=e.data;if("$"===n||"$!"===n||"$?"===n){if(0===t)return e;t--}else"/$"===n&&t++}e=e.previousSibling}return null}function cd(e,t,n){switch(t=Gu(n),e){case"html":if(!(e=t.documentElement))throw Error(l(452));return e;case"head":if(!(e=t.head))throw Error(l(453));return e;case"body":if(!(e=t.body))throw Error(l(454));return e;default:throw Error(l(451))}}var ud=new Map,dd=new Set;function fd(e){return"function"===typeof e.getRootNode?e.getRootNode():e.ownerDocument}var pd=W.d;W.d={f:function(){var e=pd.f(),t=Ic();return e||t},r:function(e){var t=Qe(e);null!==t&&5===t.tag&&"form"===t.type?pl(t):pd.r(e)},D:function(e){pd.D(e),hd("dns-prefetch",e,null)},C:function(e,t){pd.C(e,t),hd("preconnect",e,t)},L:function(e,t,n){pd.L(e,t,n);var r=md;if(r&&e&&t){var o='link[rel="preload"][as="'+ht(t)+'"]';"image"===t&&n&&n.imageSrcSet?(o+='[imagesrcset="'+ht(n.imageSrcSet)+'"]',"string"===typeof n.imageSizes&&(o+='[imagesizes="'+ht(n.imageSizes)+'"]')):o+='[href="'+ht(e)+'"]';var a=o;switch(t){case"style":a=vd(e);break;case"script":a=wd(e)}ud.has(a)||(e=R({rel:"preload",href:"image"===t&&n&&n.imageSrcSet?void 0:e,as:t},n),ud.set(a,e),null!==r.querySelector(o)||"style"===t&&r.querySelector(yd(a))||"script"===t&&r.querySelector(xd(a))||(qu(t=r.createElement("link"),"link",e),Xe(t),r.head.appendChild(t)))}},m:function(e,t){pd.m(e,t);var n=md;if(n&&e){var r=t&&"string"===typeof t.as?t.as:"script",o='link[rel="modulepreload"][as="'+ht(r)+'"][href="'+ht(e)+'"]',a=o;switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":a=wd(e)}if(!ud.has(a)&&(e=R({rel:"modulepreload",href:e},t),ud.set(a,e),null===n.querySelector(o))){switch(r){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(n.querySelector(xd(a)))return}qu(r=n.createElement("link"),"link",e),Xe(r),n.head.appendChild(r)}}},X:function(e,t){pd.X(e,t);var n=md;if(n&&e){var r=Ye(n).hoistableScripts,o=wd(e),a=r.get(o);a||((a=n.querySelector(xd(o)))||(e=R({src:e,async:!0},t),(t=ud.get(o))&&Ed(e,t),Xe(a=n.createElement("script")),qu(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}},S:function(e,t,n){pd.S(e,t,n);var r=md;if(r&&e){var o=Ye(r).hoistableStyles,a=vd(e);t=t||"default";var l=o.get(a);if(!l){var i={loading:0,preload:null};if(l=r.querySelector(yd(a)))i.loading=5;else{e=R({rel:"stylesheet",href:e,"data-precedence":t},n),(n=ud.get(a))&&Nd(e,n);var s=l=r.createElement("link");Xe(s),qu(s,"link",e),s._p=new Promise((function(e,t){s.onload=e,s.onerror=t})),s.addEventListener("load",(function(){i.loading|=1})),s.addEventListener("error",(function(){i.loading|=2})),i.loading|=4,Sd(l,t,r)}l={type:"stylesheet",instance:l,count:1,state:i},o.set(a,l)}}},M:function(e,t){pd.M(e,t);var n=md;if(n&&e){var r=Ye(n).hoistableScripts,o=wd(e),a=r.get(o);a||((a=n.querySelector(xd(o)))||(e=R({src:e,async:!0,type:"module"},t),(t=ud.get(o))&&Ed(e,t),Xe(a=n.createElement("script")),qu(a,"link",e),n.head.appendChild(a)),a={type:"script",instance:a,count:1,state:null},r.set(o,a))}}};var md="undefined"===typeof document?null:document;function hd(e,t,n){var r=md;if(r&&"string"===typeof t&&t){var o=ht(t);o='link[rel="'+e+'"][href="'+o+'"]',"string"===typeof n&&(o+='[crossorigin="'+n+'"]'),dd.has(o)||(dd.add(o),e={rel:e,crossOrigin:n,href:t},null===r.querySelector(o)&&(qu(t=r.createElement("link"),"link",e),Xe(t),r.head.appendChild(t)))}}function gd(e,t,n,r){var o,a,i,s,c=(c=X.current)?fd(c):null;if(!c)throw Error(l(446));switch(e){case"meta":case"title":return null;case"style":return"string"===typeof n.precedence&&"string"===typeof n.href?(t=vd(n.href),(r=(n=Ye(c).hoistableStyles).get(t))||(r={type:"style",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};case"link":if("stylesheet"===n.rel&&"string"===typeof n.href&&"string"===typeof n.precedence){e=vd(n.href);var u=Ye(c).hoistableStyles,d=u.get(e);if(d||(c=c.ownerDocument||c,d={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,d),(u=c.querySelector(yd(e)))&&!u._p&&(d.instance=u,d.state.loading=5),ud.has(e)||(n={rel:"preload",as:"style",href:n.href,crossOrigin:n.crossOrigin,integrity:n.integrity,media:n.media,hrefLang:n.hrefLang,referrerPolicy:n.referrerPolicy},ud.set(e,n),u||(o=c,a=e,i=n,s=d.state,o.querySelector('link[rel="preload"][as="style"]['+a+"]")?s.loading=1:(a=o.createElement("link"),s.preload=a,a.addEventListener("load",(function(){return s.loading|=1})),a.addEventListener("error",(function(){return s.loading|=2})),qu(a,"link",i),Xe(a),o.head.appendChild(a))))),t&&null===r)throw Error(l(528,""));return d}if(t&&null!==r)throw Error(l(529,""));return null;case"script":return t=n.async,"string"===typeof(n=n.src)&&t&&"function"!==typeof t&&"symbol"!==typeof t?(t=wd(n),(r=(n=Ye(c).hoistableScripts).get(t))||(r={type:"script",instance:null,count:0,state:null},n.set(t,r)),r):{type:"void",instance:null,count:0,state:null};default:throw Error(l(444,e))}}function vd(e){return'href="'+ht(e)+'"'}function yd(e){return'link[rel="stylesheet"]['+e+"]"}function bd(e){return R({},e,{"data-precedence":e.precedence,precedence:null})}function wd(e){return'[src="'+ht(e)+'"]'}function xd(e){return"script[async]"+e}function kd(e,t,n){if(t.count++,null===t.instance)switch(t.type){case"style":var r=e.querySelector('style[data-href~="'+ht(n.href)+'"]');if(r)return t.instance=r,Xe(r),r;var o=R({},n,{"data-href":n.href,"data-precedence":n.precedence,href:null,precedence:null});return Xe(r=(e.ownerDocument||e).createElement("style")),qu(r,"style",o),Sd(r,n.precedence,e),t.instance=r;case"stylesheet":o=vd(n.href);var a=e.querySelector(yd(o));if(a)return t.state.loading|=4,t.instance=a,Xe(a),a;r=bd(n),(o=ud.get(o))&&Nd(r,o),Xe(a=(e.ownerDocument||e).createElement("link"));var i=a;return i._p=new Promise((function(e,t){i.onload=e,i.onerror=t})),qu(a,"link",r),t.state.loading|=4,Sd(a,n.precedence,e),t.instance=a;case"script":return a=wd(n.src),(o=e.querySelector(xd(a)))?(t.instance=o,Xe(o),o):(r=n,(o=ud.get(a))&&Ed(r=R({},n),o),Xe(o=(e=e.ownerDocument||e).createElement("script")),qu(o,"link",r),e.head.appendChild(o),t.instance=o);case"void":return null;default:throw Error(l(443,t.type))}else"stylesheet"===t.type&&0===(4&t.state.loading)&&(r=t.instance,t.state.loading|=4,Sd(r,n.precedence,e));return t.instance}function Sd(e,t,n){for(var r=n.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),o=r.length?r[r.length-1]:null,a=o,l=0;l<r.length;l++){var i=r[l];if(i.dataset.precedence===t)a=i;else if(a!==o)break}a?a.parentNode.insertBefore(e,a.nextSibling):(t=9===n.nodeType?n.head:n).insertBefore(e,t.firstChild)}function Nd(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.title&&(e.title=t.title)}function Ed(e,t){null==e.crossOrigin&&(e.crossOrigin=t.crossOrigin),null==e.referrerPolicy&&(e.referrerPolicy=t.referrerPolicy),null==e.integrity&&(e.integrity=t.integrity)}var Cd=null;function jd(e,t,n){if(null===Cd){var r=new Map,o=Cd=new Map;o.set(n,r)}else(r=(o=Cd).get(n))||(r=new Map,o.set(n,r));if(r.has(e))return r;for(r.set(e,null),n=n.getElementsByTagName(e),o=0;o<n.length;o++){var a=n[o];if(!(a[$e]||a[Ie]||"link"===e&&"stylesheet"===a.getAttribute("rel"))&&"http://www.w3.org/2000/svg"!==a.namespaceURI){var l=a.getAttribute(t)||"";l=e+l;var i=r.get(l);i?i.push(a):r.set(l,[a])}}return r}function Pd(e,t,n){(e=e.ownerDocument||e).head.insertBefore(n,"title"===t?e.querySelector("head > title"):null)}function _d(e){return"stylesheet"!==e.type||0!==(3&e.state.loading)}var Td=null;function Rd(){}function Ad(){if(this.count--,0===this.count)if(this.stylesheets)Od(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}var zd=null;function Od(e,t){e.stylesheets=null,null!==e.unsuspend&&(e.count++,zd=new Map,t.forEach(Ld,e),zd=null,Ad.call(e))}function Ld(e,t){if(!(4&t.state.loading)){var n=zd.get(e);if(n)var r=n.get(null);else{n=new Map,zd.set(e,n);for(var o=e.querySelectorAll("link[data-precedence],style[data-precedence]"),a=0;a<o.length;a++){var l=o[a];"LINK"!==l.nodeName&&"not all"===l.getAttribute("media")||(n.set(l.dataset.precedence,l),r=l)}r&&n.set(null,r)}l=(o=t.instance).getAttribute("data-precedence"),(a=n.get(l)||r)===r&&n.set(null,o),n.set(l,o),this.count++,r=Ad.bind(this),o.addEventListener("load",r),o.addEventListener("error",r),a?a.parentNode.insertBefore(o,a.nextSibling):(e=9===e.nodeType?e.head:e).insertBefore(o,e.firstChild),t.state.loading|=4}}var Md={$$typeof:g,Provider:null,Consumer:null,_currentValue:V,_currentValue2:V,_threadCount:0};function Dd(e,t,n,r,o,a,l,i){this.tag=1,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Re(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.finishedLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Re(0),this.hiddenUpdates=Re(null),this.identifierPrefix=r,this.onUncaughtError=o,this.onCaughtError=a,this.onRecoverableError=l,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=i,this.incompleteTransitions=new Map}function Id(e,t,n,r,o,a,l,i,s,c,u,d){return e=new Dd(e,t,n,l,i,s,c,d),t=1,!0===a&&(t|=24),a=Ls(3,null,null,t),e.current=a,a.stateNode=e,(t=Uo()).refCount++,e.pooledCache=t,t.refCount++,a.memoizedState={element:r,isDehydrated:n,cache:t},_i(a),e}function Fd(e){return e?e=Ar:Ar}function Hd(e,t,n,r,o,a){o=Fd(o),null===r.context?r.context=o:r.pendingContext=o,(r=Ri(t)).payload={element:n},null!==(a=void 0===a?null:a)&&(r.callback=a),null!==(n=Ai(e,r,t))&&(Ac(n,0,t),zi(n,e,t))}function Ud(e,t){if(null!==(e=e.memoizedState)&&null!==e.dehydrated){var n=e.retryLane;e.retryLane=0!==n&&n<t?n:t}}function Wd(e,t){Ud(e,t),(e=e.alternate)&&Ud(e,t)}function Vd(e){if(13===e.tag){var t=_r(e,67108864);null!==t&&Ac(t,0,67108864),Wd(e,67108864)}}var Bd=!0;function $d(e,t,n,r){var o=T.T;T.T=null;var a=W.p;try{W.p=2,Kd(e,t,n,r)}finally{W.p=a,T.T=o}}function qd(e,t,n,r){var o=T.T;T.T=null;var a=W.p;try{W.p=8,Kd(e,t,n,r)}finally{W.p=a,T.T=o}}function Kd(e,t,n,r){if(Bd){var o=Qd(r);if(null===o)Ou(e,t,r,Gd,n),lf(e,r);else if(function(e,t,n,r,o){switch(t){case"focusin":return Jd=sf(Jd,e,t,n,r,o),!0;case"dragenter":return ef=sf(ef,e,t,n,r,o),!0;case"mouseover":return tf=sf(tf,e,t,n,r,o),!0;case"pointerover":var a=o.pointerId;return nf.set(a,sf(nf.get(a)||null,e,t,n,r,o)),!0;case"gotpointercapture":return a=o.pointerId,rf.set(a,sf(rf.get(a)||null,e,t,n,r,o)),!0}return!1}(o,e,t,n,r))r.stopPropagation();else if(lf(e,r),4&t&&-1<af.indexOf(e)){for(;null!==o;){var a=Qe(o);if(null!==a)switch(a.tag){case 3:if((a=a.stateNode).current.memoizedState.isDehydrated){var l=Ee(a.pendingLanes);if(0!==l){var i=a;for(i.pendingLanes|=2,i.entangledLanes|=2;l;){var s=1<<31-we(l);i.entanglements[1]|=s,l&=~s}hu(a),0===(6&ec)&&(wc=se()+500,gu(0,!1))}}break;case 13:null!==(i=_r(a,2))&&Ac(i,0,2),Ic(),Wd(a,2)}if(null===(a=Qd(r))&&Ou(e,t,r,Gd,n),a===o)break;o=a}null!==o&&r.stopPropagation()}else Ou(e,t,r,null,n)}}function Qd(e){return Yd(e=Rt(e))}var Gd=null;function Yd(e){if(Gd=null,null!==(e=Ke(e))){var t=D(e);if(null===t)e=null;else{var n=t.tag;if(13===n){if(null!==(e=I(t)))return e;e=null}else if(3===n){if(t.stateNode.current.memoizedState.isDehydrated)return 3===t.tag?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Gd=e,null}function Xd(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(ce()){case ue:return 2;case de:return 8;case fe:case pe:return 32;case me:return 268435456;default:return 32}default:return 32}}var Zd=!1,Jd=null,ef=null,tf=null,nf=new Map,rf=new Map,of=[],af="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function lf(e,t){switch(e){case"focusin":case"focusout":Jd=null;break;case"dragenter":case"dragleave":ef=null;break;case"mouseover":case"mouseout":tf=null;break;case"pointerover":case"pointerout":nf.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":rf.delete(t.pointerId)}}function sf(e,t,n,r,o,a){return null===e||e.nativeEvent!==a?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:a,targetContainers:[o]},null!==t&&(null!==(t=Qe(t))&&Vd(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,null!==o&&-1===t.indexOf(o)&&t.push(o),e)}function cf(e){var t=Ke(e.target);if(null!==t){var n=D(t);if(null!==n)if(13===(t=n.tag)){if(null!==(t=I(n)))return e.blockedOn=t,void function(e,t){var n=W.p;try{return W.p=e,t()}finally{W.p=n}}(e.priority,(function(){if(13===n.tag){var e=Tc(),t=_r(n,e);null!==t&&Ac(t,0,e),Wd(n,e)}}))}else if(3===t&&n.stateNode.current.memoizedState.isDehydrated)return void(e.blockedOn=3===n.tag?n.stateNode.containerInfo:null)}e.blockedOn=null}function uf(e){if(null!==e.blockedOn)return!1;for(var t=e.targetContainers;0<t.length;){var n=Qd(e.nativeEvent);if(null!==n)return null!==(t=Qe(n))&&Vd(t),e.blockedOn=n,!1;var r=new(n=e.nativeEvent).constructor(n.type,n);Tt=r,n.target.dispatchEvent(r),Tt=null,t.shift()}return!0}function df(e,t,n){uf(e)&&n.delete(t)}function ff(){Zd=!1,null!==Jd&&uf(Jd)&&(Jd=null),null!==ef&&uf(ef)&&(ef=null),null!==tf&&uf(tf)&&(tf=null),nf.forEach(df),rf.forEach(df)}function pf(e,t){e.blockedOn===t&&(e.blockedOn=null,Zd||(Zd=!0,r.unstable_scheduleCallback(r.unstable_NormalPriority,ff)))}var mf=null;function hf(e){mf!==e&&(mf=e,r.unstable_scheduleCallback(r.unstable_NormalPriority,(function(){mf===e&&(mf=null);for(var t=0;t<e.length;t+=3){var n=e[t],r=e[t+1],o=e[t+2];if("function"!==typeof r){if(null===Yd(r||n))continue;break}var a=Qe(n);null!==a&&(e.splice(t,3),t-=3,dl(a,{pending:!0,data:o,method:n.method,action:r},r,o))}})))}function gf(e){function t(t){return pf(t,e)}null!==Jd&&pf(Jd,e),null!==ef&&pf(ef,e),null!==tf&&pf(tf,e),nf.forEach(t),rf.forEach(t);for(var n=0;n<of.length;n++){var r=of[n];r.blockedOn===e&&(r.blockedOn=null)}for(;0<of.length&&null===(n=of[0]).blockedOn;)cf(n),null===n.blockedOn&&of.shift();if(null!=(n=(e.ownerDocument||e).$$reactFormReplay))for(r=0;r<n.length;r+=3){var o=n[r],a=n[r+1],l=o[Fe]||null;if("function"===typeof a)l||hf(n);else if(l){var i=null;if(a&&a.hasAttribute("formAction")){if(o=a,l=a[Fe]||null)i=l.formAction;else if(null!==Yd(o))continue}else i=l.action;"function"===typeof i?n[r+1]=i:(n.splice(r,3),r-=3),hf(n)}}}function vf(e){this._internalRoot=e}function yf(e){this._internalRoot=e}yf.prototype.render=vf.prototype.render=function(e){var t=this._internalRoot;if(null===t)throw Error(l(409));Hd(t.current,Tc(),e,t,null,null)},yf.prototype.unmount=vf.prototype.unmount=function(){var e=this._internalRoot;if(null!==e){this._internalRoot=null;var t=e.containerInfo;0===e.tag&&tu(),Hd(e.current,2,null,e,null,null),Ic(),t[He]=null}},yf.prototype.unstable_scheduleHydration=function(e){if(e){var t=Me();e={blockedOn:null,target:e,priority:t};for(var n=0;n<of.length&&0!==t&&t<of[n].priority;n++);of.splice(n,0,e),0===n&&cf(e)}};var bf=o.version;if("19.0.0"!==bf)throw Error(l(527,bf,"19.0.0"));W.findDOMNode=function(e){var t=e._reactInternals;if(void 0===t){if("function"===typeof e.render)throw Error(l(188));throw e=Object.keys(e).join(","),Error(l(268,e))}return e=function(e){var t=e.alternate;if(!t){if(null===(t=D(e)))throw Error(l(188));return t!==e?null:e}for(var n=e,r=t;;){var o=n.return;if(null===o)break;var a=o.alternate;if(null===a){if(null!==(r=o.return)){n=r;continue}break}if(o.child===a.child){for(a=o.child;a;){if(a===n)return F(o),e;if(a===r)return F(o),t;a=a.sibling}throw Error(l(188))}if(n.return!==r.return)n=o,r=a;else{for(var i=!1,s=o.child;s;){if(s===n){i=!0,n=o,r=a;break}if(s===r){i=!0,r=o,n=a;break}s=s.sibling}if(!i){for(s=a.child;s;){if(s===n){i=!0,n=a,r=o;break}if(s===r){i=!0,r=a,n=o;break}s=s.sibling}if(!i)throw Error(l(189))}}if(n.alternate!==r)throw Error(l(190))}if(3!==n.tag)throw Error(l(188));return n.stateNode.current===n?e:t}(t),e=null===(e=null!==e?H(e):null)?null:e.stateNode};var wf={bundleType:0,version:"19.0.0",rendererPackageName:"react-dom",currentDispatcherRef:T,findFiberByHostInstance:Ke,reconcilerVersion:"19.0.0"};if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__){var xf=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!xf.isDisabled&&xf.supportsFiber)try{ve=xf.inject(wf),ye=xf}catch(Sf){}}t.createRoot=function(e,t){if(!i(e))throw Error(l(299));var n=!1,r="",o=Ll,a=Ml,s=Dl;return null!==t&&void 0!==t&&(!0===t.unstable_strictMode&&(n=!0),void 0!==t.identifierPrefix&&(r=t.identifierPrefix),void 0!==t.onUncaughtError&&(o=t.onUncaughtError),void 0!==t.onCaughtError&&(a=t.onCaughtError),void 0!==t.onRecoverableError&&(s=t.onRecoverableError),void 0!==t.unstable_transitionCallbacks&&t.unstable_transitionCallbacks),t=Id(e,1,!1,null,0,n,r,o,a,s,0,null),e[He]=t.current,Au(8===e.nodeType?e.parentNode:e),new vf(t)},t.hydrateRoot=function(e,t,n){if(!i(e))throw Error(l(299));var r=!1,o="",a=Ll,s=Ml,c=Dl,u=null;return null!==n&&void 0!==n&&(!0===n.unstable_strictMode&&(r=!0),void 0!==n.identifierPrefix&&(o=n.identifierPrefix),void 0!==n.onUncaughtError&&(a=n.onUncaughtError),void 0!==n.onCaughtError&&(s=n.onCaughtError),void 0!==n.onRecoverableError&&(c=n.onRecoverableError),void 0!==n.unstable_transitionCallbacks&&n.unstable_transitionCallbacks,void 0!==n.formState&&(u=n.formState)),(t=Id(e,1,!0,t,0,r,o,a,s,c,0,u)).context=Fd(null),n=t.current,(o=Ri(r=Tc())).callback=null,Ai(n,o,r),t.current.lanes=r,Ae(t,r),hu(t),e[He]=t.current,Au(e),new yf(t)},t.version="19.0.0"},672:(e,t,n)=>{var r=n(43);function o(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var n=2;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function a(){}var l={d:{f:a,r:function(){throw Error(o(522))},D:a,C:a,L:a,m:a,X:a,S:a,M:a},p:0,findDOMNode:null},i=Symbol.for("react.portal");var s=r.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function c(e,t){return"font"===e?"":"string"===typeof t?"use-credentials"===t?t:"":void 0}t.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=l,t.createPortal=function(e,t){var n=2<arguments.length&&void 0!==arguments[2]?arguments[2]:null;if(!t||1!==t.nodeType&&9!==t.nodeType&&11!==t.nodeType)throw Error(o(299));return function(e,t,n){var r=3<arguments.length&&void 0!==arguments[3]?arguments[3]:null;return{$$typeof:i,key:null==r?null:""+r,children:e,containerInfo:t,implementation:n}}(e,t,null,n)},t.flushSync=function(e){var t=s.T,n=l.p;try{if(s.T=null,l.p=2,e)return e()}finally{s.T=t,l.p=n,l.d.f()}},t.preconnect=function(e,t){"string"===typeof e&&(t?t="string"===typeof(t=t.crossOrigin)?"use-credentials"===t?t:"":void 0:t=null,l.d.C(e,t))},t.prefetchDNS=function(e){"string"===typeof e&&l.d.D(e)},t.preinit=function(e,t){if("string"===typeof e&&t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin),o="string"===typeof t.integrity?t.integrity:void 0,a="string"===typeof t.fetchPriority?t.fetchPriority:void 0;"style"===n?l.d.S(e,"string"===typeof t.precedence?t.precedence:void 0,{crossOrigin:r,integrity:o,fetchPriority:a}):"script"===n&&l.d.X(e,{crossOrigin:r,integrity:o,fetchPriority:a,nonce:"string"===typeof t.nonce?t.nonce:void 0})}},t.preinitModule=function(e,t){if("string"===typeof e)if("object"===typeof t&&null!==t){if(null==t.as||"script"===t.as){var n=c(t.as,t.crossOrigin);l.d.M(e,{crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0})}}else null==t&&l.d.M(e)},t.preload=function(e,t){if("string"===typeof e&&"object"===typeof t&&null!==t&&"string"===typeof t.as){var n=t.as,r=c(n,t.crossOrigin);l.d.L(e,n,{crossOrigin:r,integrity:"string"===typeof t.integrity?t.integrity:void 0,nonce:"string"===typeof t.nonce?t.nonce:void 0,type:"string"===typeof t.type?t.type:void 0,fetchPriority:"string"===typeof t.fetchPriority?t.fetchPriority:void 0,referrerPolicy:"string"===typeof t.referrerPolicy?t.referrerPolicy:void 0,imageSrcSet:"string"===typeof t.imageSrcSet?t.imageSrcSet:void 0,imageSizes:"string"===typeof t.imageSizes?t.imageSizes:void 0,media:"string"===typeof t.media?t.media:void 0})}},t.preloadModule=function(e,t){if("string"===typeof e)if(t){var n=c(t.as,t.crossOrigin);l.d.m(e,{as:"string"===typeof t.as&&"script"!==t.as?t.as:void 0,crossOrigin:n,integrity:"string"===typeof t.integrity?t.integrity:void 0})}else l.d.m(e)},t.requestFormReset=function(e){l.d.r(e)},t.unstable_batchedUpdates=function(e,t){return e(t)},t.useFormState=function(e,t,n){return s.H.useFormState(e,t,n)},t.useFormStatus=function(){return s.H.useHostTransitionStatus()},t.version="19.0.0"},391:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(4)},950:(e,t,n)=>{!function e(){if("undefined"!==typeof __REACT_DEVTOOLS_GLOBAL_HOOK__&&"function"===typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE)try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(e)}catch(t){console.error(t)}}(),e.exports=n(672)},799:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.fragment");function o(e,t,r){var o=null;if(void 0!==r&&(o=""+r),void 0!==t.key&&(o=""+t.key),"key"in t)for(var a in r={},t)"key"!==a&&(r[a]=t[a]);else r=t;return t=r.ref,{$$typeof:n,type:e,key:o,ref:void 0!==t?t:null,props:r}}t.Fragment=r,t.jsx=o,t.jsxs=o},288:(e,t)=>{var n=Symbol.for("react.transitional.element"),r=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),a=Symbol.for("react.strict_mode"),l=Symbol.for("react.profiler"),i=Symbol.for("react.consumer"),s=Symbol.for("react.context"),c=Symbol.for("react.forward_ref"),u=Symbol.for("react.suspense"),d=Symbol.for("react.memo"),f=Symbol.for("react.lazy"),p=Symbol.iterator;var m={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},h=Object.assign,g={};function v(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}function y(){}function b(e,t,n){this.props=e,this.context=t,this.refs=g,this.updater=n||m}v.prototype.isReactComponent={},v.prototype.setState=function(e,t){if("object"!==typeof e&&"function"!==typeof e&&null!=e)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")},v.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")},y.prototype=v.prototype;var w=b.prototype=new y;w.constructor=b,h(w,v.prototype),w.isPureReactComponent=!0;var x=Array.isArray,k={H:null,A:null,T:null,S:null},S=Object.prototype.hasOwnProperty;function N(e,t,r,o,a,l){return r=l.ref,{$$typeof:n,type:e,key:t,ref:void 0!==r?r:null,props:l}}function E(e){return"object"===typeof e&&null!==e&&e.$$typeof===n}var C=/\/+/g;function j(e,t){return"object"===typeof e&&null!==e&&null!=e.key?function(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,(function(e){return t[e]}))}(""+e.key):t.toString(36)}function P(){}function _(e,t,o,a,l){var i=typeof e;"undefined"!==i&&"boolean"!==i||(e=null);var s,c,u=!1;if(null===e)u=!0;else switch(i){case"bigint":case"string":case"number":u=!0;break;case"object":switch(e.$$typeof){case n:case r:u=!0;break;case f:return _((u=e._init)(e._payload),t,o,a,l)}}if(u)return l=l(e),u=""===a?"."+j(e,0):a,x(l)?(o="",null!=u&&(o=u.replace(C,"$&/")+"/"),_(l,t,o,"",(function(e){return e}))):null!=l&&(E(l)&&(s=l,c=o+(null==l.key||e&&e.key===l.key?"":(""+l.key).replace(C,"$&/")+"/")+u,l=N(s.type,c,void 0,0,0,s.props)),t.push(l)),1;u=0;var d,m=""===a?".":a+":";if(x(e))for(var h=0;h<e.length;h++)u+=_(a=e[h],t,o,i=m+j(a,h),l);else if("function"===typeof(h=null===(d=e)||"object"!==typeof d?null:"function"===typeof(d=p&&d[p]||d["@@iterator"])?d:null))for(e=h.call(e),h=0;!(a=e.next()).done;)u+=_(a=a.value,t,o,i=m+j(a,h++),l);else if("object"===i){if("function"===typeof e.then)return _(function(e){switch(e.status){case"fulfilled":return e.value;case"rejected":throw e.reason;default:switch("string"===typeof e.status?e.then(P,P):(e.status="pending",e.then((function(t){"pending"===e.status&&(e.status="fulfilled",e.value=t)}),(function(t){"pending"===e.status&&(e.status="rejected",e.reason=t)}))),e.status){case"fulfilled":return e.value;case"rejected":throw e.reason}}throw e}(e),t,o,a,l);throw t=String(e),Error("Objects are not valid as a React child (found: "+("[object Object]"===t?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.")}return u}function T(e,t,n){if(null==e)return e;var r=[],o=0;return _(e,r,"","",(function(e){return t.call(n,e,o++)})),r}function R(e){if(-1===e._status){var t=e._result;(t=t()).then((function(t){0!==e._status&&-1!==e._status||(e._status=1,e._result=t)}),(function(t){0!==e._status&&-1!==e._status||(e._status=2,e._result=t)})),-1===e._status&&(e._status=0,e._result=t)}if(1===e._status)return e._result.default;throw e._result}var A="function"===typeof reportError?reportError:function(e){if("object"===typeof window&&"function"===typeof window.ErrorEvent){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:"object"===typeof e&&null!==e&&"string"===typeof e.message?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if("object"===typeof process&&"function"===typeof process.emit)return void process.emit("uncaughtException",e);console.error(e)};function z(){}t.Children={map:T,forEach:function(e,t,n){T(e,(function(){t.apply(this,arguments)}),n)},count:function(e){var t=0;return T(e,(function(){t++})),t},toArray:function(e){return T(e,(function(e){return e}))||[]},only:function(e){if(!E(e))throw Error("React.Children.only expected to receive a single React element child.");return e}},t.Component=v,t.Fragment=o,t.Profiler=l,t.PureComponent=b,t.StrictMode=a,t.Suspense=u,t.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=k,t.act=function(){throw Error("act(...) is not supported in production builds of React.")},t.cache=function(e){return function(){return e.apply(null,arguments)}},t.cloneElement=function(e,t,n){if(null===e||void 0===e)throw Error("The argument must be a React element, but you passed "+e+".");var r=h({},e.props),o=e.key;if(null!=t)for(a in void 0!==t.ref&&void 0,void 0!==t.key&&(o=""+t.key),t)!S.call(t,a)||"key"===a||"__self"===a||"__source"===a||"ref"===a&&void 0===t.ref||(r[a]=t[a]);var a=arguments.length-2;if(1===a)r.children=n;else if(1<a){for(var l=Array(a),i=0;i<a;i++)l[i]=arguments[i+2];r.children=l}return N(e.type,o,void 0,0,0,r)},t.createContext=function(e){return(e={$$typeof:s,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null}).Provider=e,e.Consumer={$$typeof:i,_context:e},e},t.createElement=function(e,t,n){var r,o={},a=null;if(null!=t)for(r in void 0!==t.key&&(a=""+t.key),t)S.call(t,r)&&"key"!==r&&"__self"!==r&&"__source"!==r&&(o[r]=t[r]);var l=arguments.length-2;if(1===l)o.children=n;else if(1<l){for(var i=Array(l),s=0;s<l;s++)i[s]=arguments[s+2];o.children=i}if(e&&e.defaultProps)for(r in l=e.defaultProps)void 0===o[r]&&(o[r]=l[r]);return N(e,a,void 0,0,0,o)},t.createRef=function(){return{current:null}},t.forwardRef=function(e){return{$$typeof:c,render:e}},t.isValidElement=E,t.lazy=function(e){return{$$typeof:f,_payload:{_status:-1,_result:e},_init:R}},t.memo=function(e,t){return{$$typeof:d,type:e,compare:void 0===t?null:t}},t.startTransition=function(e){var t=k.T,n={};k.T=n;try{var r=e(),o=k.S;null!==o&&o(n,r),"object"===typeof r&&null!==r&&"function"===typeof r.then&&r.then(z,A)}catch(a){A(a)}finally{k.T=t}},t.unstable_useCacheRefresh=function(){return k.H.useCacheRefresh()},t.use=function(e){return k.H.use(e)},t.useActionState=function(e,t,n){return k.H.useActionState(e,t,n)},t.useCallback=function(e,t){return k.H.useCallback(e,t)},t.useContext=function(e){return k.H.useContext(e)},t.useDebugValue=function(){},t.useDeferredValue=function(e,t){return k.H.useDeferredValue(e,t)},t.useEffect=function(e,t){return k.H.useEffect(e,t)},t.useId=function(){return k.H.useId()},t.useImperativeHandle=function(e,t,n){return k.H.useImperativeHandle(e,t,n)},t.useInsertionEffect=function(e,t){return k.H.useInsertionEffect(e,t)},t.useLayoutEffect=function(e,t){return k.H.useLayoutEffect(e,t)},t.useMemo=function(e,t){return k.H.useMemo(e,t)},t.useOptimistic=function(e,t){return k.H.useOptimistic(e,t)},t.useReducer=function(e,t,n){return k.H.useReducer(e,t,n)},t.useRef=function(e){return k.H.useRef(e)},t.useState=function(e){return k.H.useState(e)},t.useSyncExternalStore=function(e,t,n){return k.H.useSyncExternalStore(e,t,n)},t.useTransition=function(){return k.H.useTransition()},t.version="19.0.0"},43:(e,t,n)=>{e.exports=n(288)},579:(e,t,n)=>{e.exports=n(799)},896:(e,t)=>{function n(e,t){var n=e.length;e.push(t);e:for(;0<n;){var r=n-1>>>1,o=e[r];if(!(0<a(o,t)))break e;e[r]=t,e[n]=o,n=r}}function r(e){return 0===e.length?null:e[0]}function o(e){if(0===e.length)return null;var t=e[0],n=e.pop();if(n!==t){e[0]=n;e:for(var r=0,o=e.length,l=o>>>1;r<l;){var i=2*(r+1)-1,s=e[i],c=i+1,u=e[c];if(0>a(s,n))c<o&&0>a(u,s)?(e[r]=u,e[c]=n,r=c):(e[r]=s,e[i]=n,r=i);else{if(!(c<o&&0>a(u,n)))break e;e[r]=u,e[c]=n,r=c}}}return t}function a(e,t){var n=e.sortIndex-t.sortIndex;return 0!==n?n:e.id-t.id}if(t.unstable_now=void 0,"object"===typeof performance&&"function"===typeof performance.now){var l=performance;t.unstable_now=function(){return l.now()}}else{var i=Date,s=i.now();t.unstable_now=function(){return i.now()-s}}var c=[],u=[],d=1,f=null,p=3,m=!1,h=!1,g=!1,v="function"===typeof setTimeout?setTimeout:null,y="function"===typeof clearTimeout?clearTimeout:null,b="undefined"!==typeof setImmediate?setImmediate:null;function w(e){for(var t=r(u);null!==t;){if(null===t.callback)o(u);else{if(!(t.startTime<=e))break;o(u),t.sortIndex=t.expirationTime,n(c,t)}t=r(u)}}function x(e){if(g=!1,w(e),!h)if(null!==r(c))h=!0,R();else{var t=r(u);null!==t&&A(x,t.startTime-e)}}var k,S=!1,N=-1,E=5,C=-1;function j(){return!(t.unstable_now()-C<E)}function P(){if(S){var e=t.unstable_now();C=e;var n=!0;try{e:{h=!1,g&&(g=!1,y(N),N=-1),m=!0;var a=p;try{t:{for(w(e),f=r(c);null!==f&&!(f.expirationTime>e&&j());){var l=f.callback;if("function"===typeof l){f.callback=null,p=f.priorityLevel;var i=l(f.expirationTime<=e);if(e=t.unstable_now(),"function"===typeof i){f.callback=i,w(e),n=!0;break t}f===r(c)&&o(c),w(e)}else o(c);f=r(c)}if(null!==f)n=!0;else{var s=r(u);null!==s&&A(x,s.startTime-e),n=!1}}break e}finally{f=null,p=a,m=!1}n=void 0}}finally{n?k():S=!1}}}if("function"===typeof b)k=function(){b(P)};else if("undefined"!==typeof MessageChannel){var _=new MessageChannel,T=_.port2;_.port1.onmessage=P,k=function(){T.postMessage(null)}}else k=function(){v(P,0)};function R(){S||(S=!0,k())}function A(e,n){N=v((function(){e(t.unstable_now())}),n)}t.unstable_IdlePriority=5,t.unstable_ImmediatePriority=1,t.unstable_LowPriority=4,t.unstable_NormalPriority=3,t.unstable_Profiling=null,t.unstable_UserBlockingPriority=2,t.unstable_cancelCallback=function(e){e.callback=null},t.unstable_continueExecution=function(){h||m||(h=!0,R())},t.unstable_forceFrameRate=function(e){0>e||125<e?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):E=0<e?Math.floor(1e3/e):5},t.unstable_getCurrentPriorityLevel=function(){return p},t.unstable_getFirstCallbackNode=function(){return r(c)},t.unstable_next=function(e){switch(p){case 1:case 2:case 3:var t=3;break;default:t=p}var n=p;p=t;try{return e()}finally{p=n}},t.unstable_pauseExecution=function(){},t.unstable_requestPaint=function(){},t.unstable_runWithPriority=function(e,t){switch(e){case 1:case 2:case 3:case 4:case 5:break;default:e=3}var n=p;p=e;try{return t()}finally{p=n}},t.unstable_scheduleCallback=function(e,o,a){var l=t.unstable_now();switch("object"===typeof a&&null!==a?a="number"===typeof(a=a.delay)&&0<a?l+a:l:a=l,e){case 1:var i=-1;break;case 2:i=250;break;case 5:i=1073741823;break;case 4:i=1e4;break;default:i=5e3}return e={id:d++,callback:o,priorityLevel:e,startTime:a,expirationTime:i=a+i,sortIndex:-1},a>l?(e.sortIndex=a,n(u,e),null===r(c)&&e===r(u)&&(g?(y(N),N=-1):g=!0,A(x,a-l))):(e.sortIndex=i,n(c,e),h||m||(h=!0,R())),e},t.unstable_shouldYield=j,t.unstable_wrapCallback=function(e){var t=p;return function(){var n=p;p=t;try{return e.apply(this,arguments)}finally{p=n}}}},853:(e,t,n)=>{e.exports=n(896)}},t={};function n(r){var o=t[r];if(void 0!==o)return o.exports;var a=t[r]={exports:{}};return e[r](a,a.exports,n),a.exports}(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;n.t=function(r,o){if(1&o&&(r=this(r)),8&o)return r;if("object"===typeof r&&r){if(4&o&&r.__esModule)return r;if(16&o&&"function"===typeof r.then)return r}var a=Object.create(null);n.r(a);var l={};e=e||[null,t({}),t([]),t(t)];for(var i=2&o&&r;"object"==typeof i&&!~e.indexOf(i);i=t(i))Object.getOwnPropertyNames(i).forEach((e=>l[e]=()=>r[e]));return l.default=()=>r,n.d(a,l),a}})(),n.d=(e,t)=>{for(var r in t)n.o(t,r)&&!n.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.nc=void 0;var r=n(43),o=n.t(r,2),a=n(391);function l(e){return l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},l(e)}function i(e){var t=function(e,t){if("object"!=l(e)||!e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,t||"default");if("object"!=l(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(e,"string");return"symbol"==l(t)?t:t+""}function s(e,t,n){return(t=i(t))in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function c(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?c(Object(n),!0).forEach((function(t){s(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):c(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}function d(e,t){if(null==e)return{};var n,r,o=function(e,t){if(null==e)return{};var n={};for(var r in e)if({}.hasOwnProperty.call(e,r)){if(t.includes(r))continue;n[r]=e[r]}return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(r=0;r<a.length;r++)n=a[r],t.includes(n)||{}.propertyIsEnumerable.call(e,n)&&(o[n]=e[n])}return o}let f=0;const p=new Map,m=e=>{if(p.has(e))return;const t=setTimeout((()=>{p.delete(e),y({type:"REMOVE_TOAST",toastId:e})}),1e6);p.set(e,t)},h=(e,t)=>{switch(t.type){case"ADD_TOAST":return u(u({},e),{},{toasts:[t.toast,...e.toasts].slice(0,1)});case"UPDATE_TOAST":return u(u({},e),{},{toasts:e.toasts.map((e=>e.id===t.toast.id?u(u({},e),t.toast):e))});case"DISMISS_TOAST":{const{toastId:n}=t;return n?m(n):e.toasts.forEach((e=>{m(e.id)})),u(u({},e),{},{toasts:e.toasts.map((e=>e.id===n||void 0===n?u(u({},e),{},{open:!1}):e))})}case"REMOVE_TOAST":return void 0===t.toastId?u(u({},e),{},{toasts:[]}):u(u({},e),{},{toasts:e.toasts.filter((e=>e.id!==t.toastId))})}},g=[];let v={toasts:[]};function y(e){v=h(v,e),g.forEach((e=>{e(v)}))}function b(e){let t=Object.assign({},(function(e){if(null==e)throw new TypeError("Cannot destructure "+e)}(e),e));const n=(f=(f+1)%Number.MAX_SAFE_INTEGER,f.toString()),r=()=>y({type:"DISMISS_TOAST",toastId:n});return y({type:"ADD_TOAST",toast:u(u({},t),{},{id:n,open:!0,onOpenChange:e=>{e||r()}})}),{id:n,dismiss:r,update:e=>y({type:"UPDATE_TOAST",toast:u(u({},e),{},{id:n})})}}function w(){const[e,t]=r.useState(v);return r.useEffect((()=>(g.push(t),()=>{const e=g.indexOf(t);e>-1&&g.splice(e,1)})),[e]),u(u({},e),{},{toast:b,dismiss:e=>y({type:"DISMISS_TOAST",toastId:e})})}var x=n(950);function k(e,t){let{checkForDefaultPrevented:n=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(r){if(null===e||void 0===e||e(r),!1===n||!r.defaultPrevented)return null===t||void 0===t?void 0:t(r)}}function S(e,t){if("function"===typeof e)return e(t);null!==e&&void 0!==e&&(e.current=t)}function N(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return e=>{let n=!1;const r=t.map((t=>{const r=S(t,e);return n||"function"!=typeof r||(n=!0),r}));if(n)return()=>{for(let e=0;e<r.length;e++){const n=r[e];"function"==typeof n?n():S(t[e],null)}}}}function E(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return r.useCallback(N(...t),t)}function C(e,t,n){(function(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")})(e,t),t.set(e,n)}function j(e,t,n){if("function"==typeof e?e===t:e.has(t))return arguments.length<3?t:n;throw new TypeError("Private element is not present on this object")}function P(e,t){return e.get(j(e,t))}function _(e,t,n){return e.set(j(e,t),n),n}var T=n(579);const R=["children"],A=["scope","children"];function z(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[];const o=()=>{const t=n.map((e=>r.createContext(e)));return function(n){const o=(null===n||void 0===n?void 0:n[e])||t;return r.useMemo((()=>({["__scope".concat(e)]:u(u({},n),{},{[e]:o})})),[n,o])}};return o.scopeName=e,[function(t,o){const a=r.createContext(o),l=n.length;n=[...n,o];const i=t=>{var n;const{scope:o,children:i}=t,s=d(t,A),c=(null===o||void 0===o||null===(n=o[e])||void 0===n?void 0:n[l])||a,u=r.useMemo((()=>s),Object.values(s));return(0,T.jsx)(c.Provider,{value:u,children:i})};return i.displayName=t+"Provider",[i,function(n,i){var s;const c=(null===i||void 0===i||null===(s=i[e])||void 0===s?void 0:s[l])||a,u=r.useContext(c);if(u)return u;if(void 0!==o)return o;throw new Error("`".concat(n,"` must be used within `").concat(t,"`"))}]},O(o,...t)]}function O(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];const o=t[0];if(1===t.length)return o;const a=()=>{const e=t.map((e=>({useScope:e(),scopeName:e.scopeName})));return function(t){const n=e.reduce(((e,n)=>{let{useScope:r,scopeName:o}=n;const a=r(t)["__scope".concat(o)];return u(u({},e),a)}),{});return r.useMemo((()=>({["__scope".concat(o.scopeName)]:n})),[n])}};return a.scopeName=o.scopeName,a}const L=["children"],M=["children"];function D(e){const t=F(e),n=r.forwardRef(((e,n)=>{const{children:o}=e,a=d(e,L),l=r.Children.toArray(o),i=l.find(W);if(i){const e=i.props.children,o=l.map((t=>t===i?r.Children.count(e)>1?r.Children.only(null):r.isValidElement(e)?e.props.children:null:t));return(0,T.jsx)(t,u(u({},a),{},{ref:n,children:r.isValidElement(e)?r.cloneElement(e,void 0,o):null}))}return(0,T.jsx)(t,u(u({},a),{},{ref:n,children:o}))}));return n.displayName="".concat(e,".Slot"),n}var I=D("Slot");function F(e){const t=r.forwardRef(((e,t)=>{const{children:n}=e,o=d(e,M);if(r.isValidElement(n)){const e=function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(n),a=function(e,t){const n=u({},t);for(const r in t){const o=e[r],a=t[r];/^on[A-Z]/.test(r)?o&&a?n[r]=function(){a(...arguments),o(...arguments)}:o&&(n[r]=o):"style"===r?n[r]=u(u({},o),a):"className"===r&&(n[r]=[o,a].filter(Boolean).join(" "))}return u(u({},e),n)}(o,n.props);return n.type!==r.Fragment&&(a.ref=t?N(t,e):e),r.cloneElement(n,a)}return r.Children.count(n)>1?r.Children.only(null):null}));return t.displayName="".concat(e,".SlotClone"),t}var H=Symbol("radix.slottable");var U;function W(e){return r.isValidElement(e)&&"function"===typeof e.type&&"__radixId"in e.type&&e.type.__radixId===H}const V=["scope","children"];function B(e){const t=e+"CollectionProvider",[n,o]=z(t),[a,l]=n(t,{collectionRef:{current:null},itemMap:new Map}),i=e=>{const{scope:t,children:n}=e,o=r.useRef(null),l=r.useRef(new Map).current;return(0,T.jsx)(a,{scope:t,itemMap:l,collectionRef:o,children:n})};i.displayName=t;const s=e+"CollectionSlot",c=D(s),f=r.forwardRef(((e,t)=>{const{scope:n,children:r}=e,o=E(t,l(s,n).collectionRef);return(0,T.jsx)(c,{ref:o,children:r})}));f.displayName=s;const p=e+"CollectionItemSlot",m="data-radix-collection-item",h=D(p),g=r.forwardRef(((e,t)=>{const{scope:n,children:o}=e,a=d(e,V),i=r.useRef(null),s=E(t,i),c=l(p,n);return r.useEffect((()=>(c.itemMap.set(i,u({ref:i},a)),()=>{c.itemMap.delete(i)}))),(0,T.jsx)(h,u(u({},{[m]:""}),{},{ref:s,children:o}))}));return g.displayName=p,[{Provider:i,Slot:f,ItemSlot:g},function(t){const n=l(e+"CollectionConsumer",t);return r.useCallback((()=>{const e=n.collectionRef.current;if(!e)return[];const t=Array.from(e.querySelectorAll("[".concat(m,"]")));return Array.from(n.itemMap.values()).sort(((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current)))}),[n.collectionRef,n.itemMap])},o]}var $=new WeakMap;U=new WeakMap,Map;function q(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);const n=function(e,t){const n=e.length,r=K(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return-1===n?void 0:e[n]}function K(e){return e!==e||0===e?0:Math.trunc(e)}const Q=["asChild"];var G=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce(((e,t)=>{const n=D("Primitive.".concat(t)),o=r.forwardRef(((e,r)=>{const{asChild:o}=e,a=d(e,Q),l=o?n:t;return"undefined"!==typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,T.jsx)(l,u(u({},a),{},{ref:r}))}));return o.displayName="Primitive.".concat(t),u(u({},e),{},{[t]:o})}),{});function Y(e,t){e&&x.flushSync((()=>e.dispatchEvent(t)))}function X(e){const t=r.useRef(e);return r.useEffect((()=>{t.current=e})),r.useMemo((()=>function(){for(var e,n=arguments.length,r=new Array(n),o=0;o<n;o++)r[o]=arguments[o];return null===(e=t.current)||void 0===e?void 0:e.call(t,...r)}),[])}const Z=["disableOutsidePointerEvents","onEscapeKeyDown","onPointerDownOutside","onFocusOutside","onInteractOutside","onDismiss"];var J,ee="dismissableLayer.update",te="dismissableLayer.pointerDownOutside",ne="dismissableLayer.focusOutside",re=r.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),oe=r.forwardRef(((e,t)=>{var n;const{disableOutsidePointerEvents:o=!1,onEscapeKeyDown:a,onPointerDownOutside:l,onFocusOutside:i,onInteractOutside:s,onDismiss:c}=e,f=d(e,Z),p=r.useContext(re),[m,h]=r.useState(null),g=null!==(n=null===m||void 0===m?void 0:m.ownerDocument)&&void 0!==n?n:null===globalThis||void 0===globalThis?void 0:globalThis.document,[,v]=r.useState({}),y=E(t,(e=>h(e))),b=Array.from(p.layers),[w]=[...p.layersWithOutsidePointerEventsDisabled].slice(-1),x=b.indexOf(w),S=m?b.indexOf(m):-1,N=p.layersWithOutsidePointerEventsDisabled.size>0,C=S>=x,j=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===globalThis||void 0===globalThis?void 0:globalThis.document;const n=X(e),o=r.useRef(!1),a=r.useRef((()=>{}));return r.useEffect((()=>{const e=e=>{if(e.target&&!o.current){let r=function(){ie(te,n,o,{discrete:!0})};const o={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",a.current),a.current=r,t.addEventListener("click",a.current,{once:!0})):r()}else t.removeEventListener("click",a.current);o.current=!1},r=window.setTimeout((()=>{t.addEventListener("pointerdown",e)}),0);return()=>{window.clearTimeout(r),t.removeEventListener("pointerdown",e),t.removeEventListener("click",a.current)}}),[t,n]),{onPointerDownCapture:()=>o.current=!0}}((e=>{const t=e.target,n=[...p.branches].some((e=>e.contains(t)));C&&!n&&(null===l||void 0===l||l(e),null===s||void 0===s||s(e),e.defaultPrevented||null===c||void 0===c||c())}),g),P=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===globalThis||void 0===globalThis?void 0:globalThis.document;const n=X(e),o=r.useRef(!1);return r.useEffect((()=>{const e=e=>{if(e.target&&!o.current){ie(ne,n,{originalEvent:e},{discrete:!1})}};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)}),[t,n]),{onFocusCapture:()=>o.current=!0,onBlurCapture:()=>o.current=!1}}((e=>{const t=e.target;[...p.branches].some((e=>e.contains(t)))||(null===i||void 0===i||i(e),null===s||void 0===s||s(e),e.defaultPrevented||null===c||void 0===c||c())}),g);return function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null===globalThis||void 0===globalThis?void 0:globalThis.document;const n=X(e);r.useEffect((()=>{const e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})}),[n,t])}((e=>{S===p.layers.size-1&&(null===a||void 0===a||a(e),!e.defaultPrevented&&c&&(e.preventDefault(),c()))}),g),r.useEffect((()=>{if(m)return o&&(0===p.layersWithOutsidePointerEventsDisabled.size&&(J=g.body.style.pointerEvents,g.body.style.pointerEvents="none"),p.layersWithOutsidePointerEventsDisabled.add(m)),p.layers.add(m),le(),()=>{o&&1===p.layersWithOutsidePointerEventsDisabled.size&&(g.body.style.pointerEvents=J)}}),[m,g,o,p]),r.useEffect((()=>()=>{m&&(p.layers.delete(m),p.layersWithOutsidePointerEventsDisabled.delete(m),le())}),[m,p]),r.useEffect((()=>{const e=()=>v({});return document.addEventListener(ee,e),()=>document.removeEventListener(ee,e)}),[]),(0,T.jsx)(G.div,u(u({},f),{},{ref:y,style:u({pointerEvents:N?C?"auto":"none":void 0},e.style),onFocusCapture:k(e.onFocusCapture,P.onFocusCapture),onBlurCapture:k(e.onBlurCapture,P.onBlurCapture),onPointerDownCapture:k(e.onPointerDownCapture,j.onPointerDownCapture)}))}));oe.displayName="DismissableLayer";var ae=r.forwardRef(((e,t)=>{const n=r.useContext(re),o=r.useRef(null),a=E(t,o);return r.useEffect((()=>{const e=o.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}}),[n.branches]),(0,T.jsx)(G.div,u(u({},e),{},{ref:a}))}));function le(){const e=new CustomEvent(ee);document.dispatchEvent(e)}function ie(e,t,n,r){let{discrete:o}=r;const a=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?Y(a,l):a.dispatchEvent(l)}ae.displayName="DismissableLayerBranch";var se=oe,ce=ae,ue=null!==globalThis&&void 0!==globalThis&&globalThis.document?r.useLayoutEffect:()=>{};const de=["container"];var fe=r.forwardRef(((e,t)=>{var n;const{container:o}=e,a=d(e,de),[l,i]=r.useState(!1);ue((()=>i(!0)),[]);const s=o||l&&(null===globalThis||void 0===globalThis||null===(n=globalThis.document)||void 0===n?void 0:n.body);return s?x.createPortal((0,T.jsx)(G.div,u(u({},a),{},{ref:t})),s):null}));fe.displayName="Portal";var pe=e=>{const{present:t,children:n}=e,o=function(e){const[t,n]=r.useState(),o=r.useRef(null),a=r.useRef(e),l=r.useRef("none"),i=e?"mounted":"unmounted",[s,c]=function(e,t){return r.useReducer(((e,n)=>{const r=t[e][n];return null!==r&&void 0!==r?r:e}),e)}(i,{mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}});return r.useEffect((()=>{const e=me(o.current);l.current="mounted"===s?e:"none"}),[s]),ue((()=>{const t=o.current,n=a.current;if(n!==e){const r=l.current,o=me(t);if(e)c("MOUNT");else if("none"===o||"none"===(null===t||void 0===t?void 0:t.display))c("UNMOUNT");else{c(n&&r!==o?"ANIMATION_OUT":"UNMOUNT")}a.current=e}}),[e,c]),ue((()=>{if(t){var e;let n;const r=null!==(e=t.ownerDocument.defaultView)&&void 0!==e?e:window,i=e=>{const l=me(o.current).includes(e.animationName);if(e.target===t&&l&&(c("ANIMATION_END"),!a.current)){const e=t.style.animationFillMode;t.style.animationFillMode="forwards",n=r.setTimeout((()=>{"forwards"===t.style.animationFillMode&&(t.style.animationFillMode=e)}))}},s=e=>{e.target===t&&(l.current=me(o.current))};return t.addEventListener("animationstart",s),t.addEventListener("animationcancel",i),t.addEventListener("animationend",i),()=>{r.clearTimeout(n),t.removeEventListener("animationstart",s),t.removeEventListener("animationcancel",i),t.removeEventListener("animationend",i)}}c("ANIMATION_END")}),[t,c]),{isPresent:["mounted","unmountSuspended"].includes(s),ref:r.useCallback((e=>{o.current=e?getComputedStyle(e):null,n(e)}),[])}}(t),a="function"===typeof n?n({present:o.isPresent}):r.Children.only(n),l=E(o.ref,function(e){var t,n;let r=null===(t=Object.getOwnPropertyDescriptor(e.props,"ref"))||void 0===t?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;if(o)return e.ref;if(r=null===(n=Object.getOwnPropertyDescriptor(e,"ref"))||void 0===n?void 0:n.get,o=r&&"isReactWarning"in r&&r.isReactWarning,o)return e.props.ref;return e.props.ref||e.ref}(a));return"function"===typeof n||o.isPresent?r.cloneElement(a,{ref:l}):null};function me(e){return(null===e||void 0===e?void 0:e.animationName)||"none"}pe.displayName="Presence";var he=o[" useInsertionEffect ".trim().toString()]||ue;function ge(e){let{prop:t,defaultProp:n,onChange:o=()=>{},caller:a}=e;const[l,i,s]=function(e){let{defaultProp:t,onChange:n}=e;const[o,a]=r.useState(t),l=r.useRef(o),i=r.useRef(n);return he((()=>{i.current=n}),[n]),r.useEffect((()=>{var e;l.current!==o&&(null===(e=i.current)||void 0===e||e.call(i,o),l.current=o)}),[o,l]),[o,a,i]}({defaultProp:n,onChange:o}),c=void 0!==t,u=c?t:l;{const e=r.useRef(void 0!==t);r.useEffect((()=>{const t=e.current;if(t!==c){const e=t?"controlled":"uncontrolled",n=c?"controlled":"uncontrolled";console.warn("".concat(a," is changing from ").concat(e," to ").concat(n,". Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component."))}e.current=c}),[c,a])}const d=r.useCallback((e=>{if(c){const r=function(e){return"function"===typeof e}(e)?e(t):e;var n;if(r!==t)null===(n=s.current)||void 0===n||n.call(s,r)}else i(e)}),[c,t,i,s]);return[u,d]}Symbol("RADIX:SYNC_STATE");var ve=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),ye=r.forwardRef(((e,t)=>(0,T.jsx)(G.span,u(u({},e),{},{ref:t,style:u(u({},ve),e.style)}))));ye.displayName="VisuallyHidden";const be=["__scopeToast","hotkey","label"],we=["__scopeToast","onFocusFromOutsideViewport"],xe=["forceMount","open","defaultOpen","onOpenChange"],ke=["__scopeToast","type","duration","open","onClose","onEscapeKeyDown","onPause","onResume","onSwipeStart","onSwipeMove","onSwipeCancel","onSwipeEnd"],Se=["__scopeToast","children"],Ne=["__scopeToast"],Ee=["__scopeToast"],Ce=["altText"],je=["__scopeToast"],Pe=["__scopeToast","altText"];var _e="ToastProvider",[Te,Re,Ae]=B("Toast"),[ze,Oe]=z("Toast",[Ae]),[Le,Me]=ze(_e),De=e=>{const{__scopeToast:t,label:n="Notification",duration:o=5e3,swipeDirection:a="right",swipeThreshold:l=50,children:i}=e,[s,c]=r.useState(null),[u,d]=r.useState(0),f=r.useRef(!1),p=r.useRef(!1);return n.trim()||console.error("Invalid prop `label` supplied to `".concat(_e,"`. Expected non-empty `string`.")),(0,T.jsx)(Te.Provider,{scope:t,children:(0,T.jsx)(Le,{scope:t,label:n,duration:o,swipeDirection:a,swipeThreshold:l,toastCount:u,viewport:s,onViewportChange:c,onToastAdd:r.useCallback((()=>d((e=>e+1))),[]),onToastRemove:r.useCallback((()=>d((e=>e-1))),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:i})})};De.displayName=_e;var Ie="ToastViewport",Fe=["F8"],He="toast.viewportPause",Ue="toast.viewportResume",We=r.forwardRef(((e,t)=>{const{__scopeToast:n,hotkey:o=Fe,label:a="Notifications ({hotkey})"}=e,l=d(e,be),i=Me(Ie,n),s=Re(n),c=r.useRef(null),f=r.useRef(null),p=r.useRef(null),m=r.useRef(null),h=E(t,m,i.onViewportChange),g=o.join("+").replace(/Key/g,"").replace(/Digit/g,""),v=i.toastCount>0;r.useEffect((()=>{const e=e=>{var t;0!==o.length&&o.every((t=>e[t]||e.code===t))&&(null===(t=m.current)||void 0===t||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)}),[o]),r.useEffect((()=>{const e=c.current,t=m.current;if(v&&e&&t){const n=()=>{if(!i.isClosePausedRef.current){const e=new CustomEvent(He);t.dispatchEvent(e),i.isClosePausedRef.current=!0}},r=()=>{if(i.isClosePausedRef.current){const e=new CustomEvent(Ue);t.dispatchEvent(e),i.isClosePausedRef.current=!1}},o=t=>{!e.contains(t.relatedTarget)&&r()},a=()=>{e.contains(document.activeElement)||r()};return e.addEventListener("focusin",n),e.addEventListener("focusout",o),e.addEventListener("pointermove",n),e.addEventListener("pointerleave",a),window.addEventListener("blur",n),window.addEventListener("focus",r),()=>{e.removeEventListener("focusin",n),e.removeEventListener("focusout",o),e.removeEventListener("pointermove",n),e.removeEventListener("pointerleave",a),window.removeEventListener("blur",n),window.removeEventListener("focus",r)}}}),[v,i.isClosePausedRef]);const y=r.useCallback((e=>{let{tabbingDirection:t}=e;const n=s().map((e=>{const n=e.ref.current,r=[n,...it(n)];return"forwards"===t?r:r.reverse()}));return("forwards"===t?n.reverse():n).flat()}),[s]);return r.useEffect((()=>{const e=m.current;if(e){const t=t=>{const n=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!n){const n=document.activeElement,l=t.shiftKey;var r;if(t.target===e&&l)return void(null===(r=f.current)||void 0===r||r.focus());const i=y({tabbingDirection:l?"backwards":"forwards"}),s=i.findIndex((e=>e===n));var o,a;if(st(i.slice(s+1)))t.preventDefault();else l?null===(o=f.current)||void 0===o||o.focus():null===(a=p.current)||void 0===a||a.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}}),[s,y]),(0,T.jsxs)(ce,{ref:c,role:"region","aria-label":a.replace("{hotkey}",g),tabIndex:-1,style:{pointerEvents:v?void 0:"none"},children:[v&&(0,T.jsx)(Be,{ref:f,onFocusFromOutsideViewport:()=>{st(y({tabbingDirection:"forwards"}))}}),(0,T.jsx)(Te.Slot,{scope:n,children:(0,T.jsx)(G.ol,u(u({tabIndex:-1},l),{},{ref:h}))}),v&&(0,T.jsx)(Be,{ref:p,onFocusFromOutsideViewport:()=>{st(y({tabbingDirection:"backwards"}))}})]})}));We.displayName=Ie;var Ve="ToastFocusProxy",Be=r.forwardRef(((e,t)=>{const{__scopeToast:n,onFocusFromOutsideViewport:r}=e,o=d(e,we),a=Me(Ve,n);return(0,T.jsx)(ye,u(u({"aria-hidden":!0,tabIndex:0},o),{},{ref:t,style:{position:"fixed"},onFocus:e=>{var t;const n=e.relatedTarget;!(null!==(t=a.viewport)&&void 0!==t&&t.contains(n))&&r()}}))}));Be.displayName=Ve;var $e="Toast",qe=r.forwardRef(((e,t)=>{const{forceMount:n,open:r,defaultOpen:o,onOpenChange:a}=e,l=d(e,xe),[i,s]=ge({prop:r,defaultProp:null===o||void 0===o||o,onChange:a,caller:$e});return(0,T.jsx)(pe,{present:n||i,children:(0,T.jsx)(Ge,u(u({open:i},l),{},{ref:t,onClose:()=>s(!1),onPause:X(e.onPause),onResume:X(e.onResume),onSwipeStart:k(e.onSwipeStart,(e=>{e.currentTarget.setAttribute("data-swipe","start")})),onSwipeMove:k(e.onSwipeMove,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(n,"px"))})),onSwipeCancel:k(e.onSwipeCancel,(e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")})),onSwipeEnd:k(e.onSwipeEnd,(e=>{const{x:t,y:n}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(n,"px")),s(!1)}))}))})}));qe.displayName=$e;var[Ke,Qe]=ze($e,{onClose(){}}),Ge=r.forwardRef(((e,t)=>{const{__scopeToast:n,type:o="foreground",duration:a,open:l,onClose:i,onEscapeKeyDown:s,onPause:c,onResume:f,onSwipeStart:p,onSwipeMove:m,onSwipeCancel:h,onSwipeEnd:g}=e,v=d(e,ke),y=Me($e,n),[b,w]=r.useState(null),S=E(t,(e=>w(e))),N=r.useRef(null),C=r.useRef(null),j=a||y.duration,P=r.useRef(0),_=r.useRef(j),R=r.useRef(0),{onToastAdd:A,onToastRemove:z}=y,O=X((()=>{var e;(null===b||void 0===b?void 0:b.contains(document.activeElement))&&(null===(e=y.viewport)||void 0===e||e.focus()),i()})),L=r.useCallback((e=>{e&&e!==1/0&&(window.clearTimeout(R.current),P.current=(new Date).getTime(),R.current=window.setTimeout(O,e))}),[O]);r.useEffect((()=>{const e=y.viewport;if(e){const t=()=>{L(_.current),null===f||void 0===f||f()},n=()=>{const e=(new Date).getTime()-P.current;_.current=_.current-e,window.clearTimeout(R.current),null===c||void 0===c||c()};return e.addEventListener(He,n),e.addEventListener(Ue,t),()=>{e.removeEventListener(He,n),e.removeEventListener(Ue,t)}}}),[y.viewport,j,c,f,L]),r.useEffect((()=>{l&&!y.isClosePausedRef.current&&L(j)}),[l,j,y.isClosePausedRef,L]),r.useEffect((()=>(A(),()=>z())),[A,z]);const M=r.useMemo((()=>b?ot(b):null),[b]);return y.viewport?(0,T.jsxs)(T.Fragment,{children:[M&&(0,T.jsx)(Ye,{__scopeToast:n,role:"status","aria-live":"foreground"===o?"assertive":"polite","aria-atomic":!0,children:M}),(0,T.jsx)(Ke,{scope:n,onClose:O,children:x.createPortal((0,T.jsx)(Te.ItemSlot,{scope:n,children:(0,T.jsx)(se,{asChild:!0,onEscapeKeyDown:k(s,(()=>{y.isFocusedToastEscapeKeyDownRef.current||O(),y.isFocusedToastEscapeKeyDownRef.current=!1})),children:(0,T.jsx)(G.li,u(u({role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":l?"open":"closed","data-swipe-direction":y.swipeDirection},v),{},{ref:S,style:u({userSelect:"none",touchAction:"none"},e.style),onKeyDown:k(e.onKeyDown,(e=>{"Escape"===e.key&&(null===s||void 0===s||s(e.nativeEvent),e.nativeEvent.defaultPrevented||(y.isFocusedToastEscapeKeyDownRef.current=!0,O()))})),onPointerDown:k(e.onPointerDown,(e=>{0===e.button&&(N.current={x:e.clientX,y:e.clientY})})),onPointerMove:k(e.onPointerMove,(e=>{if(!N.current)return;const t=e.clientX-N.current.x,n=e.clientY-N.current.y,r=Boolean(C.current),o=["left","right"].includes(y.swipeDirection),a=["left","up"].includes(y.swipeDirection)?Math.min:Math.max,l=o?a(0,t):0,i=o?0:a(0,n),s="touch"===e.pointerType?10:2,c={x:l,y:i},u={originalEvent:e,delta:c};r?(C.current=c,at("toast.swipeMove",m,u,{discrete:!1})):lt(c,y.swipeDirection,s)?(C.current=c,at("toast.swipeStart",p,u,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>s||Math.abs(n)>s)&&(N.current=null)})),onPointerUp:k(e.onPointerUp,(e=>{const t=C.current,n=e.target;if(n.hasPointerCapture(e.pointerId)&&n.releasePointerCapture(e.pointerId),C.current=null,N.current=null,t){const n=e.currentTarget,r={originalEvent:e,delta:t};lt(t,y.swipeDirection,y.swipeThreshold)?at("toast.swipeEnd",g,r,{discrete:!0}):at("toast.swipeCancel",h,r,{discrete:!0}),n.addEventListener("click",(e=>e.preventDefault()),{once:!0})}}))}))})}),y.viewport)})]}):null})),Ye=e=>{const{__scopeToast:t,children:n}=e,o=d(e,Se),a=Me($e,t),[l,i]=r.useState(!1),[s,c]=r.useState(!1);return function(){const e=X(arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{});ue((()=>{let t=0,n=0;return t=window.requestAnimationFrame((()=>n=window.requestAnimationFrame(e))),()=>{window.cancelAnimationFrame(t),window.cancelAnimationFrame(n)}}),[e])}((()=>i(!0))),r.useEffect((()=>{const e=window.setTimeout((()=>c(!0)),1e3);return()=>window.clearTimeout(e)}),[]),s?null:(0,T.jsx)(fe,{asChild:!0,children:(0,T.jsx)(ye,u(u({},o),{},{children:l&&(0,T.jsxs)(T.Fragment,{children:[a.label," ",n]})}))})},Xe=r.forwardRef(((e,t)=>{const{__scopeToast:n}=e,r=d(e,Ne);return(0,T.jsx)(G.div,u(u({},r),{},{ref:t}))}));Xe.displayName="ToastTitle";var Ze=r.forwardRef(((e,t)=>{const{__scopeToast:n}=e,r=d(e,Ee);return(0,T.jsx)(G.div,u(u({},r),{},{ref:t}))}));Ze.displayName="ToastDescription";var Je="ToastAction",et=r.forwardRef(((e,t)=>{const{altText:n}=e,r=d(e,Ce);return n.trim()?(0,T.jsx)(rt,{altText:n,asChild:!0,children:(0,T.jsx)(nt,u(u({},r),{},{ref:t}))}):(console.error("Invalid prop `altText` supplied to `".concat(Je,"`. Expected non-empty `string`.")),null)}));et.displayName=Je;var tt="ToastClose",nt=r.forwardRef(((e,t)=>{const{__scopeToast:n}=e,r=d(e,je),o=Qe(tt,n);return(0,T.jsx)(rt,{asChild:!0,children:(0,T.jsx)(G.button,u(u({type:"button"},r),{},{ref:t,onClick:k(e.onClick,o.onClose)}))})}));nt.displayName=tt;var rt=r.forwardRef(((e,t)=>{const{__scopeToast:n,altText:r}=e,o=d(e,Pe);return(0,T.jsx)(G.div,u(u({"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":r||void 0},o),{},{ref:t}))}));function ot(e){const t=[];return Array.from(e.childNodes).forEach((e=>{if(e.nodeType===e.TEXT_NODE&&e.textContent&&t.push(e.textContent),function(e){return e.nodeType===e.ELEMENT_NODE}(e)){const n=e.ariaHidden||e.hidden||"none"===e.style.display,r=""===e.dataset.radixToastAnnounceExclude;if(!n)if(r){const n=e.dataset.radixToastAnnounceAlt;n&&t.push(n)}else t.push(...ot(e))}})),t}function at(e,t,n,r){let{discrete:o}=r;const a=n.originalEvent.currentTarget,l=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:n});t&&a.addEventListener(e,t,{once:!0}),o?Y(a,l):a.dispatchEvent(l)}var lt=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0;const r=Math.abs(e.x),o=Math.abs(e.y),a=r>o;return"left"===t||"right"===t?a&&r>n:!a&&o>n};function it(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function st(e){const t=document.activeElement;return e.some((e=>e===t||(e.focus(),document.activeElement!==t)))}var ct=De,ut=We,dt=qe,ft=Xe,pt=Ze,mt=et,ht=nt;function gt(e){var t,n,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(n=gt(e[t]))&&(r&&(r+=" "),r+=n)}else for(n in e)e[n]&&(r&&(r+=" "),r+=n);return r}function vt(){for(var e,t,n=0,r="",o=arguments.length;n<o;n++)(e=arguments[n])&&(t=gt(e))&&(r&&(r+=" "),r+=t);return r}const yt=["class","className"],bt=e=>"boolean"===typeof e?"".concat(e):0===e?"0":e,wt=vt,xt=(e,t)=>n=>{var r;if(null==(null===t||void 0===t?void 0:t.variants))return wt(e,null===n||void 0===n?void 0:n.class,null===n||void 0===n?void 0:n.className);const{variants:o,defaultVariants:a}=t,l=Object.keys(o).map((e=>{const t=null===n||void 0===n?void 0:n[e],r=null===a||void 0===a?void 0:a[e];if(null===t)return null;const l=bt(t)||bt(r);return o[e][l]})),i=n&&Object.entries(n).reduce(((e,t)=>{let[n,r]=t;return void 0===r||(e[n]=r),e}),{}),s=null===t||void 0===t||null===(r=t.compoundVariants)||void 0===r?void 0:r.reduce(((e,t)=>{let{class:n,className:r}=t,o=d(t,yt);return Object.entries(o).every((e=>{let[t,n]=e;return Array.isArray(n)?n.includes(u(u({},a),i)[t]):u(u({},a),i)[t]===n}))?[...e,n,r]:e}),[]);return wt(e,l,s,null===n||void 0===n?void 0:n.class,null===n||void 0===n?void 0:n.className)},kt=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,((e,t,n)=>n?n.toUpperCase():t.toLowerCase())))(e);return t.charAt(0).toUpperCase()+t.slice(1)},St=function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return t.filter(((e,t,n)=>Boolean(e)&&""!==e.trim()&&n.indexOf(e)===t)).join(" ").trim()};var Nt={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};const Et=["color","size","strokeWidth","absoluteStrokeWidth","className","children","iconNode"],Ct=(0,r.forwardRef)(((e,t)=>{let{color:n="currentColor",size:o=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:i="",children:s,iconNode:c}=e,f=d(e,Et);return(0,r.createElement)("svg",u(u(u({ref:t},Nt),{},{width:o,height:o,stroke:n,strokeWidth:l?24*Number(a)/Number(o):a,className:St("lucide",i)},!s&&!(e=>{for(const t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"}),f),[...c.map((e=>{let[t,n]=e;return(0,r.createElement)(t,n)})),...Array.isArray(s)?s:[s]])})),jt=["className"],Pt=(e,t)=>{const n=(0,r.forwardRef)(((n,o)=>{let{className:a}=n,l=d(n,jt);return(0,r.createElement)(Ct,u({ref:o,iconNode:t,className:St("lucide-".concat((i=kt(e),i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase())),"lucide-".concat(e),a)},l));var i}));return n.displayName=kt(e),n},_t=Pt("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),Tt=(e,t)=>{var n;if(0===e.length)return t.classGroupId;const r=e[0],o=t.nextPart.get(r),a=o?Tt(e.slice(1),o):void 0;if(a)return a;if(0===t.validators.length)return;const l=e.join("-");return null===(n=t.validators.find((e=>{let{validator:t}=e;return t(l)})))||void 0===n?void 0:n.classGroupId},Rt=/^\[(.+)\]$/,At=e=>{if(Rt.test(e)){const t=Rt.exec(e)[1],n=null===t||void 0===t?void 0:t.substring(0,t.indexOf(":"));if(n)return"arbitrary.."+n}},zt=e=>{const{theme:t,classGroups:n}=e,r={nextPart:new Map,validators:[]};for(const o in n)Ot(n[o],r,o,t);return r},Ot=(e,t,n,r)=>{e.forEach((e=>{if("string"!==typeof e){if("function"===typeof e)return Mt(e)?void Ot(e(r),t,n,r):void t.validators.push({validator:e,classGroupId:n});Object.entries(e).forEach((e=>{let[o,a]=e;Ot(a,Lt(t,o),n,r)}))}else{(""===e?t:Lt(t,e)).classGroupId=n}}))},Lt=(e,t)=>{let n=e;return t.split("-").forEach((e=>{n.nextPart.has(e)||n.nextPart.set(e,{nextPart:new Map,validators:[]}),n=n.nextPart.get(e)})),n},Mt=e=>e.isThemeGetter,Dt=e=>{if(e<1)return{get:()=>{},set:()=>{}};let t=0,n=new Map,r=new Map;const o=(o,a)=>{n.set(o,a),t++,t>e&&(t=0,r=n,n=new Map)};return{get(e){let t=n.get(e);return void 0!==t?t:void 0!==(t=r.get(e))?(o(e,t),t):void 0},set(e,t){n.has(e)?n.set(e,t):o(e,t)}}},It=e=>{const{prefix:t,experimentalParseClassName:n}=e;let r=e=>{const t=[];let n,r=0,o=0,a=0;for(let s=0;s<e.length;s++){let l=e[s];if(0===r&&0===o){if(":"===l){t.push(e.slice(a,s)),a=s+1;continue}if("/"===l){n=s;continue}}"["===l?r++:"]"===l?r--:"("===l?o++:")"===l&&o--}const l=0===t.length?e:e.substring(a),i=Ft(l);return{modifiers:t,hasImportantModifier:i!==l,baseClassName:i,maybePostfixModifierPosition:n&&n>a?n-a:void 0}};if(t){const e=t+":",n=r;r=t=>t.startsWith(e)?n(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(n){const e=r;r=t=>n({className:t,parseClassName:e})}return r},Ft=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,Ht=e=>{const t=Object.fromEntries(e.orderSensitiveModifiers.map((e=>[e,!0])));return e=>{if(e.length<=1)return e;const n=[];let r=[];return e.forEach((e=>{"["===e[0]||t[e]?(n.push(...r.sort(),e),r=[]):r.push(e)})),n.push(...r.sort()),n}},Ut=e=>u({cache:Dt(e.cacheSize),parseClassName:It(e),sortModifiers:Ht(e)},(e=>{const t=zt(e),{conflictingClassGroups:n,conflictingClassGroupModifiers:r}=e;return{getClassGroupId:e=>{const n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),Tt(n,t)||At(e)},getConflictingClassGroupIds:(e,t)=>{const o=n[e]||[];return t&&r[e]?[...o,...r[e]]:o}}})(e)),Wt=/\s+/;function Vt(){let e,t,n=0,r="";for(;n<arguments.length;)(e=arguments[n++])&&(t=Bt(e))&&(r&&(r+=" "),r+=t);return r}const Bt=e=>{if("string"===typeof e)return e;let t,n="";for(let r=0;r<e.length;r++)e[r]&&(t=Bt(e[r]))&&(n&&(n+=" "),n+=t);return n};function $t(e){for(var t=arguments.length,n=new Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];let o,a,l,i=function(t){const r=n.reduce(((e,t)=>t(e)),e());return o=Ut(r),a=o.cache.get,l=o.cache.set,i=s,s(t)};function s(e){const t=a(e);if(t)return t;const n=((e,t)=>{const{parseClassName:n,getClassGroupId:r,getConflictingClassGroupIds:o,sortModifiers:a}=t,l=[],i=e.trim().split(Wt);let s="";for(let c=i.length-1;c>=0;c-=1){const e=i[c],{isExternal:t,modifiers:u,hasImportantModifier:d,baseClassName:f,maybePostfixModifierPosition:p}=n(e);if(t){s=e+(s.length>0?" "+s:s);continue}let m=!!p,h=r(m?f.substring(0,p):f);if(!h){if(!m){s=e+(s.length>0?" "+s:s);continue}if(h=r(f),!h){s=e+(s.length>0?" "+s:s);continue}m=!1}const g=a(u).join(":"),v=d?g+"!":g,y=v+h;if(l.includes(y))continue;l.push(y);const b=o(h,m);for(let n=0;n<b.length;++n){const e=b[n];l.push(v+e)}s=e+(s.length>0?" "+s:s)}return s})(e,o);return l(e,n),n}return function(){return i(Vt.apply(null,arguments))}}const qt=e=>{const t=t=>t[e]||[];return t.isThemeGetter=!0,t},Kt=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,Qt=/^\((?:(\w[\w-]*):)?(.+)\)$/i,Gt=/^\d+\/\d+$/,Yt=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,Xt=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,Zt=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,Jt=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,en=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,tn=e=>Gt.test(e),nn=e=>!!e&&!Number.isNaN(Number(e)),rn=e=>!!e&&Number.isInteger(Number(e)),on=e=>e.endsWith("%")&&nn(e.slice(0,-1)),an=e=>Yt.test(e),ln=()=>!0,sn=e=>Xt.test(e)&&!Zt.test(e),cn=()=>!1,un=e=>Jt.test(e),dn=e=>en.test(e),fn=e=>!mn(e)&&!wn(e),pn=e=>jn(e,Rn,cn),mn=e=>Kt.test(e),hn=e=>jn(e,An,sn),gn=e=>jn(e,zn,nn),vn=e=>jn(e,_n,cn),yn=e=>jn(e,Tn,dn),bn=e=>jn(e,Ln,un),wn=e=>Qt.test(e),xn=e=>Pn(e,An),kn=e=>Pn(e,On),Sn=e=>Pn(e,_n),Nn=e=>Pn(e,Rn),En=e=>Pn(e,Tn),Cn=e=>Pn(e,Ln,!0),jn=(e,t,n)=>{const r=Kt.exec(e);return!!r&&(r[1]?t(r[1]):n(r[2]))},Pn=function(e,t){let n=arguments.length>2&&void 0!==arguments[2]&&arguments[2];const r=Qt.exec(e);return!!r&&(r[1]?t(r[1]):n)},_n=e=>"position"===e||"percentage"===e,Tn=e=>"image"===e||"url"===e,Rn=e=>"length"===e||"size"===e||"bg-size"===e,An=e=>"length"===e,zn=e=>"number"===e,On=e=>"family-name"===e,Ln=e=>"shadow"===e,Mn=(Symbol.toStringTag,()=>{const e=qt("color"),t=qt("font"),n=qt("text"),r=qt("font-weight"),o=qt("tracking"),a=qt("leading"),l=qt("breakpoint"),i=qt("container"),s=qt("spacing"),c=qt("radius"),u=qt("shadow"),d=qt("inset-shadow"),f=qt("text-shadow"),p=qt("drop-shadow"),m=qt("blur"),h=qt("perspective"),g=qt("aspect"),v=qt("ease"),y=qt("animate"),b=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",wn,mn],w=()=>[wn,mn,s],x=()=>[tn,"full","auto",...w()],k=()=>[rn,"none","subgrid",wn,mn],S=()=>["auto",{span:["full",rn,wn,mn]},rn,wn,mn],N=()=>[rn,"auto",wn,mn],E=()=>["auto","min","max","fr",wn,mn],C=()=>["auto",...w()],j=()=>[tn,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...w()],P=()=>[e,wn,mn],_=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",Sn,vn,{position:[wn,mn]}],T=()=>["auto","cover","contain",Nn,pn,{size:[wn,mn]}],R=()=>[on,xn,hn],A=()=>["","none","full",c,wn,mn],z=()=>["",nn,xn,hn],O=()=>[nn,on,Sn,vn],L=()=>["","none",m,wn,mn],M=()=>["none",nn,wn,mn],D=()=>["none",nn,wn,mn],I=()=>[nn,wn,mn],F=()=>[tn,"full",...w()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[an],breakpoint:[an],color:[ln],container:[an],"drop-shadow":[an],ease:["in","out","in-out"],font:[fn],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[an],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[an],shadow:[an],spacing:["px",nn],text:[an],"text-shadow":[an],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",tn,mn,wn,g]}],container:["container"],columns:[{columns:[nn,mn,wn,i]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:b()}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:x()}],"inset-x":[{"inset-x":x()}],"inset-y":[{"inset-y":x()}],start:[{start:x()}],end:[{end:x()}],top:[{top:x()}],right:[{right:x()}],bottom:[{bottom:x()}],left:[{left:x()}],visibility:["visible","invisible","collapse"],z:[{z:[rn,"auto",wn,mn]}],basis:[{basis:[tn,"full","auto",i,...w()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[nn,tn,"auto","initial","none",mn]}],grow:[{grow:["",nn,wn,mn]}],shrink:[{shrink:["",nn,wn,mn]}],order:[{order:[rn,"first","last","none",wn,mn]}],"grid-cols":[{"grid-cols":k()}],"col-start-end":[{col:S()}],"col-start":[{"col-start":N()}],"col-end":[{"col-end":N()}],"grid-rows":[{"grid-rows":k()}],"row-start-end":[{row:S()}],"row-start":[{"row-start":N()}],"row-end":[{"row-end":N()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":E()}],"auto-rows":[{"auto-rows":E()}],gap:[{gap:w()}],"gap-x":[{"gap-x":w()}],"gap-y":[{"gap-y":w()}],"justify-content":[{justify:["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe","normal"]}],"justify-items":[{"justify-items":["start","end","center","stretch","center-safe","end-safe","normal"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"align-items":[{items:["start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"align-self":[{self:["auto","start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"place-items":[{"place-items":["start","end","center","stretch","center-safe","end-safe","baseline"]}],"place-self":[{"place-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],p:[{p:w()}],px:[{px:w()}],py:[{py:w()}],ps:[{ps:w()}],pe:[{pe:w()}],pt:[{pt:w()}],pr:[{pr:w()}],pb:[{pb:w()}],pl:[{pl:w()}],m:[{m:C()}],mx:[{mx:C()}],my:[{my:C()}],ms:[{ms:C()}],me:[{me:C()}],mt:[{mt:C()}],mr:[{mr:C()}],mb:[{mb:C()}],ml:[{ml:C()}],"space-x":[{"space-x":w()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":w()}],"space-y-reverse":["space-y-reverse"],size:[{size:j()}],w:[{w:[i,"screen",...j()]}],"min-w":[{"min-w":[i,"screen","none",...j()]}],"max-w":[{"max-w":[i,"screen","none","prose",{screen:[l]},...j()]}],h:[{h:["screen",...j()]}],"min-h":[{"min-h":["screen","none",...j()]}],"max-h":[{"max-h":["screen",...j()]}],"font-size":[{text:["base",n,xn,hn]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[r,wn,gn]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",on,mn]}],"font-family":[{font:[kn,mn,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[o,wn,mn]}],"line-clamp":[{"line-clamp":[nn,"none",wn,gn]}],leading:[{leading:[a,...w()]}],"list-image":[{"list-image":["none",wn,mn]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",wn,mn]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:P()}],"text-color":[{text:P()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","wavy"]}],"text-decoration-thickness":[{decoration:[nn,"from-font","auto",wn,hn]}],"text-decoration-color":[{decoration:P()}],"underline-offset":[{"underline-offset":[nn,"auto",wn,mn]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:w()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",wn,mn]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",wn,mn]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:_()}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:T()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},rn,wn,mn],radial:["",wn,mn],conic:[rn,wn,mn]},En,yn]}],"bg-color":[{bg:P()}],"gradient-from-pos":[{from:R()}],"gradient-via-pos":[{via:R()}],"gradient-to-pos":[{to:R()}],"gradient-from":[{from:P()}],"gradient-via":[{via:P()}],"gradient-to":[{to:P()}],rounded:[{rounded:A()}],"rounded-s":[{"rounded-s":A()}],"rounded-e":[{"rounded-e":A()}],"rounded-t":[{"rounded-t":A()}],"rounded-r":[{"rounded-r":A()}],"rounded-b":[{"rounded-b":A()}],"rounded-l":[{"rounded-l":A()}],"rounded-ss":[{"rounded-ss":A()}],"rounded-se":[{"rounded-se":A()}],"rounded-ee":[{"rounded-ee":A()}],"rounded-es":[{"rounded-es":A()}],"rounded-tl":[{"rounded-tl":A()}],"rounded-tr":[{"rounded-tr":A()}],"rounded-br":[{"rounded-br":A()}],"rounded-bl":[{"rounded-bl":A()}],"border-w":[{border:z()}],"border-w-x":[{"border-x":z()}],"border-w-y":[{"border-y":z()}],"border-w-s":[{"border-s":z()}],"border-w-e":[{"border-e":z()}],"border-w-t":[{"border-t":z()}],"border-w-r":[{"border-r":z()}],"border-w-b":[{"border-b":z()}],"border-w-l":[{"border-l":z()}],"divide-x":[{"divide-x":z()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":z()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:["solid","dashed","dotted","double","hidden","none"]}],"divide-style":[{divide:["solid","dashed","dotted","double","hidden","none"]}],"border-color":[{border:P()}],"border-color-x":[{"border-x":P()}],"border-color-y":[{"border-y":P()}],"border-color-s":[{"border-s":P()}],"border-color-e":[{"border-e":P()}],"border-color-t":[{"border-t":P()}],"border-color-r":[{"border-r":P()}],"border-color-b":[{"border-b":P()}],"border-color-l":[{"border-l":P()}],"divide-color":[{divide:P()}],"outline-style":[{outline:["solid","dashed","dotted","double","none","hidden"]}],"outline-offset":[{"outline-offset":[nn,wn,mn]}],"outline-w":[{outline:["",nn,xn,hn]}],"outline-color":[{outline:P()}],shadow:[{shadow:["","none",u,Cn,bn]}],"shadow-color":[{shadow:P()}],"inset-shadow":[{"inset-shadow":["none",d,Cn,bn]}],"inset-shadow-color":[{"inset-shadow":P()}],"ring-w":[{ring:z()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:P()}],"ring-offset-w":[{"ring-offset":[nn,hn]}],"ring-offset-color":[{"ring-offset":P()}],"inset-ring-w":[{"inset-ring":z()}],"inset-ring-color":[{"inset-ring":P()}],"text-shadow":[{"text-shadow":["none",f,Cn,bn]}],"text-shadow-color":[{"text-shadow":P()}],opacity:[{opacity:[nn,wn,mn]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[nn]}],"mask-image-linear-from-pos":[{"mask-linear-from":O()}],"mask-image-linear-to-pos":[{"mask-linear-to":O()}],"mask-image-linear-from-color":[{"mask-linear-from":P()}],"mask-image-linear-to-color":[{"mask-linear-to":P()}],"mask-image-t-from-pos":[{"mask-t-from":O()}],"mask-image-t-to-pos":[{"mask-t-to":O()}],"mask-image-t-from-color":[{"mask-t-from":P()}],"mask-image-t-to-color":[{"mask-t-to":P()}],"mask-image-r-from-pos":[{"mask-r-from":O()}],"mask-image-r-to-pos":[{"mask-r-to":O()}],"mask-image-r-from-color":[{"mask-r-from":P()}],"mask-image-r-to-color":[{"mask-r-to":P()}],"mask-image-b-from-pos":[{"mask-b-from":O()}],"mask-image-b-to-pos":[{"mask-b-to":O()}],"mask-image-b-from-color":[{"mask-b-from":P()}],"mask-image-b-to-color":[{"mask-b-to":P()}],"mask-image-l-from-pos":[{"mask-l-from":O()}],"mask-image-l-to-pos":[{"mask-l-to":O()}],"mask-image-l-from-color":[{"mask-l-from":P()}],"mask-image-l-to-color":[{"mask-l-to":P()}],"mask-image-x-from-pos":[{"mask-x-from":O()}],"mask-image-x-to-pos":[{"mask-x-to":O()}],"mask-image-x-from-color":[{"mask-x-from":P()}],"mask-image-x-to-color":[{"mask-x-to":P()}],"mask-image-y-from-pos":[{"mask-y-from":O()}],"mask-image-y-to-pos":[{"mask-y-to":O()}],"mask-image-y-from-color":[{"mask-y-from":P()}],"mask-image-y-to-color":[{"mask-y-to":P()}],"mask-image-radial":[{"mask-radial":[wn,mn]}],"mask-image-radial-from-pos":[{"mask-radial-from":O()}],"mask-image-radial-to-pos":[{"mask-radial-to":O()}],"mask-image-radial-from-color":[{"mask-radial-from":P()}],"mask-image-radial-to-color":[{"mask-radial-to":P()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"]}],"mask-image-conic-pos":[{"mask-conic":[nn]}],"mask-image-conic-from-pos":[{"mask-conic-from":O()}],"mask-image-conic-to-pos":[{"mask-conic-to":O()}],"mask-image-conic-from-color":[{"mask-conic-from":P()}],"mask-image-conic-to-color":[{"mask-conic-to":P()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:_()}],"mask-repeat":[{mask:["no-repeat",{repeat:["","x","y","space","round"]}]}],"mask-size":[{mask:T()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",wn,mn]}],filter:[{filter:["","none",wn,mn]}],blur:[{blur:L()}],brightness:[{brightness:[nn,wn,mn]}],contrast:[{contrast:[nn,wn,mn]}],"drop-shadow":[{"drop-shadow":["","none",p,Cn,bn]}],"drop-shadow-color":[{"drop-shadow":P()}],grayscale:[{grayscale:["",nn,wn,mn]}],"hue-rotate":[{"hue-rotate":[nn,wn,mn]}],invert:[{invert:["",nn,wn,mn]}],saturate:[{saturate:[nn,wn,mn]}],sepia:[{sepia:["",nn,wn,mn]}],"backdrop-filter":[{"backdrop-filter":["","none",wn,mn]}],"backdrop-blur":[{"backdrop-blur":L()}],"backdrop-brightness":[{"backdrop-brightness":[nn,wn,mn]}],"backdrop-contrast":[{"backdrop-contrast":[nn,wn,mn]}],"backdrop-grayscale":[{"backdrop-grayscale":["",nn,wn,mn]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[nn,wn,mn]}],"backdrop-invert":[{"backdrop-invert":["",nn,wn,mn]}],"backdrop-opacity":[{"backdrop-opacity":[nn,wn,mn]}],"backdrop-saturate":[{"backdrop-saturate":[nn,wn,mn]}],"backdrop-sepia":[{"backdrop-sepia":["",nn,wn,mn]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":w()}],"border-spacing-x":[{"border-spacing-x":w()}],"border-spacing-y":[{"border-spacing-y":w()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",wn,mn]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[nn,"initial",wn,mn]}],ease:[{ease:["linear","initial",v,wn,mn]}],delay:[{delay:[nn,wn,mn]}],animate:[{animate:["none",y,wn,mn]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,wn,mn]}],"perspective-origin":[{"perspective-origin":b()}],rotate:[{rotate:M()}],"rotate-x":[{"rotate-x":M()}],"rotate-y":[{"rotate-y":M()}],"rotate-z":[{"rotate-z":M()}],scale:[{scale:D()}],"scale-x":[{"scale-x":D()}],"scale-y":[{"scale-y":D()}],"scale-z":[{"scale-z":D()}],"scale-3d":["scale-3d"],skew:[{skew:I()}],"skew-x":[{"skew-x":I()}],"skew-y":[{"skew-y":I()}],transform:[{transform:[wn,mn,"","none","gpu","cpu"]}],"transform-origin":[{origin:b()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:F()}],"translate-x":[{"translate-x":F()}],"translate-y":[{"translate-y":F()}],"translate-z":[{"translate-z":F()}],"translate-none":["translate-none"],accent:[{accent:P()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:P()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",wn,mn]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":w()}],"scroll-mx":[{"scroll-mx":w()}],"scroll-my":[{"scroll-my":w()}],"scroll-ms":[{"scroll-ms":w()}],"scroll-me":[{"scroll-me":w()}],"scroll-mt":[{"scroll-mt":w()}],"scroll-mr":[{"scroll-mr":w()}],"scroll-mb":[{"scroll-mb":w()}],"scroll-ml":[{"scroll-ml":w()}],"scroll-p":[{"scroll-p":w()}],"scroll-px":[{"scroll-px":w()}],"scroll-py":[{"scroll-py":w()}],"scroll-ps":[{"scroll-ps":w()}],"scroll-pe":[{"scroll-pe":w()}],"scroll-pt":[{"scroll-pt":w()}],"scroll-pr":[{"scroll-pr":w()}],"scroll-pb":[{"scroll-pb":w()}],"scroll-pl":[{"scroll-pl":w()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",wn,mn]}],fill:[{fill:["none",...P()]}],"stroke-w":[{stroke:[nn,xn,hn,gn]}],stroke:[{stroke:["none",...P()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}}),Dn=$t(Mn);function In(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return Dn(vt(t))}const Fn=["className"],Hn=["className","variant"],Un=["className"],Wn=["className"],Vn=["className"],Bn=["className"],$n=ct,qn=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,Fn);return(0,T.jsx)(ut,u({ref:t,className:In("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",n)},r))}));qn.displayName=ut.displayName;const Kn=xt("group pointer-events-auto relative flex w-full items-center justify-between space-x-2 overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),Qn=r.forwardRef(((e,t)=>{let{className:n,variant:r}=e,o=d(e,Hn);return(0,T.jsx)(dt,u({ref:t,className:In(Kn({variant:r}),n)},o))}));Qn.displayName=dt.displayName;r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,Un);return(0,T.jsx)(mt,u({ref:t,className:In("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",n)},r))})).displayName=mt.displayName;const Gn=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,Wn);return(0,T.jsx)(ht,u(u({ref:t,className:In("absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",n),"toast-close":""},r),{},{children:(0,T.jsx)(_t,{className:"h-4 w-4"})}))}));Gn.displayName=ht.displayName;const Yn=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,Vn);return(0,T.jsx)(ft,u({ref:t,className:In("text-sm font-semibold [&+div]:text-xs",n)},r))}));Yn.displayName=ft.displayName;const Xn=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,Bn);return(0,T.jsx)(pt,u({ref:t,className:In("text-sm opacity-90",n)},r))}));Xn.displayName=pt.displayName;const Zn=["id","title","description","action"];function Jn(){const{toasts:e}=w();return(0,T.jsxs)($n,{children:[e.map((function(e){let{id:t,title:n,description:r,action:o}=e,a=d(e,Zn);return(0,T.jsxs)(Qn,u(u({},a),{},{children:[(0,T.jsxs)("div",{className:"grid gap-1",children:[n&&(0,T.jsx)(Yn,{children:n}),r&&(0,T.jsx)(Xn,{children:r})]}),o,(0,T.jsx)(Gn,{})]}),t)})),(0,T.jsx)(qn,{})]})}const er=["className","variant","size","asChild"],tr=xt("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground shadow hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",outline:"border border-input shadow-sm hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2",sm:"h-8 rounded-md px-3 text-xs",lg:"h-10 rounded-md px-8",icon:"h-9 w-9"}},defaultVariants:{variant:"default",size:"default"}}),nr=r.forwardRef(((e,t)=>{let{className:n,variant:r,size:o,asChild:a=!1}=e,l=d(e,er);const i=a?I:"button";return(0,T.jsx)(i,u({className:In(tr({variant:r,size:o,className:n})),ref:t},l))}));nr.displayName="Button";const rr=Pt("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]]),or=()=>{const[e,t]=(0,r.useState)(!1),[n,o]=(0,r.useState)(!1);(0,r.useEffect)((()=>{const e=()=>{o(window.scrollY>20)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)}),[]);const a=[{label:"Home",href:"#home"},{label:"About",href:"#about"},{label:"Services",href:"#services"},{label:"Projects",href:"#projects"},{label:"Contact",href:"#contact"}];return(0,T.jsx)("header",{className:"fixed top-0 left-0 right-0 z-50 transition-all duration-300 ".concat(n?"bg-black/80 backdrop-blur-md border-b border-gray-800/50":"bg-transparent"),children:(0,T.jsxs)("div",{className:"container mx-auto px-6 py-4",children:[(0,T.jsxs)("div",{className:"flex items-center justify-between",children:[(0,T.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,T.jsx)("img",{src:"https://customer-assets.emergentagent.com/job_futurehome/artifacts/ctjqr19b_Genrec_Mini_Logo-removebg-preview.png",alt:"Genrec AI",className:"w-10 h-10"}),(0,T.jsx)("img",{src:"https://customer-assets.emergentagent.com/job_futurehome/artifacts/0fkp6gzh_Genrec_Full_Logo-removebg-preview.png",alt:"GENREC",className:"h-8"})]}),(0,T.jsxs)("nav",{className:"hidden md:flex items-center space-x-8",children:[a.map((e=>(0,T.jsx)("a",{href:e.href,className:"text-gray-300 hover:text-white transition-colors duration-200 text-sm font-medium",children:e.label},e.label))),(0,T.jsx)(nr,{className:"bg-gradient-to-r from-yellow-400/10 to-yellow-600/10 border border-yellow-400/30 text-yellow-400 hover:bg-yellow-400/20 transition-all duration-200",children:"Get Started"})]}),(0,T.jsx)("button",{onClick:()=>t(!e),className:"md:hidden text-white p-2",children:e?(0,T.jsx)(_t,{size:24}):(0,T.jsx)(rr,{size:24})})]}),e&&(0,T.jsx)("nav",{className:"md:hidden mt-4 pb-4",children:(0,T.jsxs)("div",{className:"flex flex-col space-y-4",children:[a.map((e=>(0,T.jsx)("a",{href:e.href,onClick:()=>t(!1),className:"text-gray-300 hover:text-white transition-colors duration-200 text-sm font-medium py-2",children:e.label},e.label))),(0,T.jsx)(nr,{className:"bg-gradient-to-r from-yellow-400/10 to-yellow-600/10 border border-yellow-400/30 text-yellow-400 hover:bg-yellow-400/20 transition-all duration-200 w-full mt-4",children:"Get Started"})]})})]})})},ar=Pt("zap",[["path",{d:"M4 14a1 1 0 0 1-.78-1.63l9.9-10.2a.5.5 0 0 1 .86.46l-1.92 6.02A1 1 0 0 0 13 10h7a1 1 0 0 1 .78 1.63l-9.9 10.2a.5.5 0 0 1-.86-.46l1.92-6.02A1 1 0 0 0 11 14z",key:"1xq2db"}]]),lr=Pt("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]),ir=Pt("trending-up",[["polyline",{points:"22 7 13.5 15.5 8.5 10.5 2 17",key:"126l90"}],["polyline",{points:"16 7 22 7 22 13",key:"kwv8wd"}]]),sr=Pt("cpu",[["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M17 20v2",key:"1rnc9c"}],["path",{d:"M17 2v2",key:"11trls"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M2 17h2",key:"7oei6x"}],["path",{d:"M2 7h2",key:"asdhe0"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"M20 17h2",key:"1fpfkl"}],["path",{d:"M20 7h2",key:"1o8tra"}],["path",{d:"M7 20v2",key:"4gnj0m"}],["path",{d:"M7 2v2",key:"1i4yhu"}],["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"8",y:"8",width:"8",height:"8",rx:"1",key:"z9xiuo"}]]),cr=Pt("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),ur=()=>{const[e,t]=(0,r.useState)(""),[n,o]=(0,r.useState)(0),[a,l]=(0,r.useState)(!0),i=["thinks, learns, and evolves.","adapts to your business.","transforms your vision.","powers your success."];(0,r.useEffect)((()=>{const r=setTimeout((()=>{const r=i[n];a?e.length<r.length?t(r.slice(0,e.length+1)):setTimeout((()=>l(!1)),2e3):e.length>0?t(e.slice(0,-1)):(o((n+1)%i.length),l(!0))}),a?100:50);return()=>clearTimeout(r)}),[e,n,a,i]);const s=[{icon:ar,number:"50+",label:"AI Projects Delivered"},{icon:lr,number:"15+",label:"Industries Served"},{icon:ir,number:"99%",label:"Client Satisfaction"},{icon:sr,number:"24/7",label:"AI Support"}];return(0,T.jsxs)("section",{id:"home",className:"min-h-screen bg-black relative overflow-hidden",children:[(0,T.jsxs)("div",{className:"absolute inset-0",children:[(0,T.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br from-black via-gray-900 to-black opacity-90"}),(0,T.jsx)("div",{className:"absolute inset-0",children:(0,T.jsx)("div",{className:"absolute inset-0 opacity-20",children:(0,T.jsx)("div",{className:"grid grid-cols-20 grid-rows-20 gap-4 h-full w-full",children:Array.from({length:400},((e,t)=>(0,T.jsx)("div",{className:"w-1 h-1 bg-yellow-400 rounded-full animate-pulse",style:{animationDelay:"".concat(3*Math.random(),"s"),animationDuration:"".concat(2+2*Math.random(),"s")}},t)))})})})]}),(0,T.jsxs)("div",{className:"relative z-10 container mx-auto px-6 pt-32 pb-20",children:[(0,T.jsxs)("div",{className:"text-center",children:[(0,T.jsx)("div",{className:"flex justify-center items-center mb-8",children:(0,T.jsxs)("div",{className:"relative",children:[(0,T.jsx)("img",{src:"https://customer-assets.emergentagent.com/job_futurehome/artifacts/ctjqr19b_Genrec_Mini_Logo-removebg-preview.png",alt:"Genrec AI Logo",className:"w-24 h-24 animate-pulse"}),(0,T.jsx)("div",{className:"absolute inset-0 bg-yellow-400/20 rounded-full blur-xl animate-pulse"})]})}),(0,T.jsxs)("h1",{className:"text-6xl md:text-8xl font-bold text-white mb-6",children:[(0,T.jsx)("span",{className:"bg-gradient-to-r from-white via-gray-100 to-gray-300 bg-clip-text text-transparent",children:"GENREC"}),(0,T.jsx)("span",{className:"block text-4xl md:text-5xl bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent font-light",children:"ARTIFICIAL INTELLIGENCE"})]}),(0,T.jsx)("div",{className:"h-24 mb-8 flex items-center justify-center",children:(0,T.jsxs)("p",{className:"text-2xl md:text-3xl text-gray-300 font-light",children:["We build AI that"," ",(0,T.jsx)("span",{className:"text-yellow-400 font-medium border-r-2 border-yellow-400 animate-pulse",children:e})]})}),(0,T.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center mb-16",children:[(0,T.jsx)(nr,{size:"lg",className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-black hover:from-yellow-300 hover:to-yellow-500 font-medium px-8 py-4 text-lg shadow-lg hover:shadow-yellow-400/25 transition-all duration-300",children:"Start Your AI Journey"}),(0,T.jsx)(nr,{size:"lg",variant:"outline",className:"border-2 border-yellow-400/30 text-yellow-400 hover:bg-yellow-400/10 font-medium px-8 py-4 text-lg backdrop-blur-sm",children:"Explore Projects"})]}),(0,T.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto",children:s.map(((e,t)=>{const n=e.icon;return(0,T.jsxs)("div",{className:"group text-center p-6 bg-gray-900/30 backdrop-blur-sm border border-gray-800/50 rounded-lg hover:bg-gray-800/40 hover:border-yellow-400/30 transition-all duration-300",children:[(0,T.jsx)(n,{className:"w-8 h-8 text-yellow-400 mx-auto mb-3 group-hover:scale-110 transition-transform duration-300"}),(0,T.jsx)("div",{className:"text-3xl font-bold text-white mb-1",children:e.number}),(0,T.jsx)("div",{className:"text-gray-400 text-sm font-medium",children:e.label})]},t)}))})]}),(0,T.jsx)("div",{className:"absolute bottom-8 left-1/2 transform -translate-x-1/2",children:(0,T.jsx)(cr,{className:"w-6 h-6 text-yellow-400 animate-bounce"})})]})]})},dr=["className"],fr=["className"],pr=["className"],mr=["className"],hr=["className"],gr=["className"],vr=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,dr);return(0,T.jsx)("div",u({ref:t,className:In("rounded-xl border bg-card text-card-foreground shadow",n)},r))}));vr.displayName="Card";const yr=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,fr);return(0,T.jsx)("div",u({ref:t,className:In("flex flex-col space-y-1.5 p-6",n)},r))}));yr.displayName="CardHeader";const br=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,pr);return(0,T.jsx)("div",u({ref:t,className:In("font-semibold leading-none tracking-tight",n)},r))}));br.displayName="CardTitle";r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,mr);return(0,T.jsx)("div",u({ref:t,className:In("text-sm text-muted-foreground",n)},r))})).displayName="CardDescription";const wr=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,hr);return(0,T.jsx)("div",u({ref:t,className:In("p-6 pt-0",n)},r))}));wr.displayName="CardContent";r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,gr);return(0,T.jsx)("div",u({ref:t,className:In("flex items-center p-6 pt-0",n)},r))})).displayName="CardFooter";const xr=Pt("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),kr=()=>{const e=[{title:"Privacy-First Architecture",description:"Zero-knowledge systems that protect your data while maximizing AI capabilities.",icon:xr},{title:"End-to-End Delivery",description:"From concept to deployment, we handle every aspect of your AI transformation.",icon:ar},{title:"Scalable Solutions",description:"Built for growth with cloud-native architecture and future-proof design.",icon:ir},{title:"24/7 AI Support",description:"Our AI systems monitor and optimize themselves, ensuring peak performance.",icon:sr}];return(0,T.jsx)("section",{id:"about",className:"py-20 bg-gradient-to-b from-black to-gray-900",children:(0,T.jsxs)("div",{className:"container mx-auto px-6",children:[(0,T.jsxs)("div",{className:"text-center mb-16",children:[(0,T.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold mb-6",children:[(0,T.jsx)("span",{className:"bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent",children:"Forging the"}),(0,T.jsx)("span",{className:"block bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent",children:"Future"})]}),(0,T.jsx)("div",{className:"max-w-3xl mx-auto",children:(0,T.jsx)("p",{className:"text-xl text-gray-300 leading-relaxed",children:"At Genrec AI, we don't just build software\u2014we craft intelligent ecosystems that evolve, adapt, and anticipate. Our mission is to bridge the gap between human creativity and machine intelligence, creating solutions that feel intuitive yet perform beyond expectations."})})]}),(0,T.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-20",children:e.map(((e,t)=>{const n=e.icon;return(0,T.jsx)(vr,{className:"group bg-gray-900/50 backdrop-blur-sm border-gray-800/50 hover:bg-gray-800/60 hover:border-yellow-400/30 transition-all duration-500 hover:-translate-y-2",children:(0,T.jsxs)(wr,{className:"p-8",children:[(0,T.jsx)("div",{className:"mb-6",children:(0,T.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-yellow-400/20 to-yellow-600/20 rounded-full flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300",children:(0,T.jsx)(n,{className:"w-8 h-8 text-yellow-400"})})}),(0,T.jsx)("h3",{className:"text-xl font-semibold text-white mb-3 group-hover:text-yellow-400 transition-colors duration-300",children:e.title}),(0,T.jsx)("p",{className:"text-gray-400 leading-relaxed",children:e.description})]})},t)}))}),(0,T.jsx)("div",{className:"bg-gradient-to-r from-gray-900/80 to-black/80 backdrop-blur-sm rounded-2xl border border-gray-800/50 p-8",children:(0,T.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-8",children:[{number:"10+",label:"MVPs Built"},{number:"5",label:"Tools Launched"},{number:"100%",label:"Uptime"},{number:"3",label:"Years Experience"}].map(((e,t)=>(0,T.jsxs)("div",{className:"text-center",children:[(0,T.jsx)("div",{className:"text-4xl md:text-5xl font-bold text-yellow-400 mb-2",children:e.number}),(0,T.jsx)("div",{className:"text-gray-300 text-sm font-medium",children:e.label})]},t)))})})]})})},Sr=["className","variant"],Nr=xt("inline-flex items-center rounded-md border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground shadow hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground shadow hover:bg-destructive/80",outline:"text-foreground"}},defaultVariants:{variant:"default"}});function Er(e){let{className:t,variant:n}=e,r=d(e,Sr);return(0,T.jsx)("div",u({className:In(Nr({variant:n}),t)},r))}const Cr=Pt("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),jr=Pt("brain",[["path",{d:"M12 5a3 3 0 1 0-5.997.125 4 4 0 0 0-2.526 5.77 4 4 0 0 0 .556 6.588A4 4 0 1 0 12 18Z",key:"l5xja"}],["path",{d:"M12 5a3 3 0 1 1 5.997.125 4 4 0 0 1 2.526 5.77 4 4 0 0 1-.556 6.588A4 4 0 1 1 12 18Z",key:"ep3f8r"}],["path",{d:"M15 13a4.5 4.5 0 0 1-3-4 4.5 4.5 0 0 1-3 4",key:"1p4c4q"}],["path",{d:"M17.599 6.5a3 3 0 0 0 .399-1.375",key:"tmeiqw"}],["path",{d:"M6.003 5.125A3 3 0 0 0 6.401 6.5",key:"105sqy"}],["path",{d:"M3.477 10.896a4 4 0 0 1 .585-.396",key:"ql3yin"}],["path",{d:"M19.938 10.5a4 4 0 0 1 .585.396",key:"1qfode"}],["path",{d:"M6 18a4 4 0 0 1-1.967-.516",key:"2e4loj"}],["path",{d:"M19.967 17.484A4 4 0 0 1 18 18",key:"159ez6"}]]),Pr=Pt("smartphone",[["rect",{width:"14",height:"20",x:"5",y:"2",rx:"2",ry:"2",key:"1yt0o3"}],["path",{d:"M12 18h.01",key:"mhygvu"}]]),_r=Pt("server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]]),Tr=Pt("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]),Rr=Pt("shopping-cart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]]),Ar=Pt("graduation-cap",[["path",{d:"M21.42 10.922a1 1 0 0 0-.019-1.838L12.83 5.18a2 2 0 0 0-1.66 0L2.6 9.08a1 1 0 0 0 0 1.832l8.57 3.908a2 2 0 0 0 1.66 0z",key:"j76jl0"}],["path",{d:"M22 10v6",key:"1lu8f3"}],["path",{d:"M6 12.5V16a6 3 0 0 0 12 0v-3.5",key:"1r8lef"}]]),zr=()=>{const e=[{title:"AI-Integrated Websites",description:"Stunning websites powered by intelligent algorithms, featuring 3D models, parallax effects, and dynamic content adaptation.",features:["3D WebGL Integration","Smart Content Adaptation","Parallax Animations","AI-Driven UX"],icon:Cr,color:"from-blue-500/20 to-cyan-500/20",borderColor:"border-cyan-500/30"},{title:"Intelligent CRM Systems",description:"Next-generation customer relationship management with predictive analytics and automated workflows.",features:["Predictive Analytics","Automated Workflows","Smart Dashboards","Real-time Insights"],icon:lr,color:"from-green-500/20 to-emerald-500/20",borderColor:"border-emerald-500/30"},{title:"Data Science MVPs",description:"Rapid prototyping of machine learning solutions with custom pipelines and predictive models.",features:["Custom ML Models","Data Pipelines","Predictive Analytics","Real-time Processing"],icon:jr,color:"from-purple-500/20 to-pink-500/20",borderColor:"border-pink-500/30"},{title:"AI-Powered Mobile Apps",description:"React Native and Flutter applications with sensor integration and offline-first architecture.",features:["Cross-Platform","Sensor Integration","Offline Sync","Push Notifications"],icon:Pr,color:"from-orange-500/20 to-red-500/20",borderColor:"border-red-500/30"},{title:"Web Services & APIs",description:"Scalable microservices architecture with REST/GraphQL APIs and Kubernetes deployment.",features:["Microservices","Auto-Scaling","API Gateway","Monitoring"],icon:_r,color:"from-gray-500/20 to-slate-500/20",borderColor:"border-slate-500/30"},{title:"Zen Analyzer",description:"Upload CSV/Excel files and get AI-driven insights, summaries, and interactive Q&A capabilities.",features:["Natural Language Queries","Chart Generation","Data Export","Interactive Analysis"],icon:Tr,color:"from-yellow-500/20 to-amber-500/20",borderColor:"border-amber-500/30"},{title:"Smart E-commerce",description:"Headless storefronts with AI personalization, AR product previews, and intelligent recommendations.",features:["AI Personalization","AR Previews","Smart Recommendations","Headless Architecture"],icon:Rr,color:"from-indigo-500/20 to-blue-500/20",borderColor:"border-blue-500/30"},{title:"Educational AI",description:"Adaptive learning platforms with AI tutors, anonymous feedback systems, and progress tracking.",features:["Adaptive Learning","AI Tutoring","Progress Analytics","Anonymous Feedback"],icon:Ar,color:"from-teal-500/20 to-cyan-500/20",borderColor:"border-teal-500/30"}];return(0,T.jsx)("section",{id:"services",className:"py-20 bg-gradient-to-b from-gray-900 to-black",children:(0,T.jsxs)("div",{className:"container mx-auto px-6",children:[(0,T.jsxs)("div",{className:"text-center mb-16",children:[(0,T.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold mb-6",children:[(0,T.jsx)("span",{className:"bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent",children:"Our"}),(0,T.jsx)("span",{className:"block bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent",children:"Services"})]}),(0,T.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"From AI-integrated websites to enterprise-grade machine learning solutions, we deliver cutting-edge technology that drives results."})]}),(0,T.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6",children:e.map(((e,t)=>{const n=e.icon;return(0,T.jsxs)(vr,{className:"group bg-gray-900/50 backdrop-blur-sm border-gray-800/50 hover:border-yellow-400/30 transition-all duration-500 hover:-translate-y-2 hover:shadow-lg hover:shadow-yellow-400/10",children:[(0,T.jsxs)(yr,{className:"pb-4",children:[(0,T.jsx)("div",{className:"mb-4",children:(0,T.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r ".concat(e.color," rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300"),children:(0,T.jsx)(n,{className:"w-8 h-8 text-yellow-400"})})}),(0,T.jsx)(br,{className:"text-xl font-semibold text-white group-hover:text-yellow-400 transition-colors duration-300",children:e.title})]}),(0,T.jsxs)(wr,{className:"pt-0",children:[(0,T.jsx)("p",{className:"text-gray-400 text-sm leading-relaxed mb-4",children:e.description}),(0,T.jsx)("div",{className:"flex flex-wrap gap-2",children:e.features.map(((e,t)=>(0,T.jsx)(Er,{variant:"outline",className:"text-xs bg-gray-800/50 border-gray-700/50 text-gray-300 hover:border-yellow-400/30 hover:text-yellow-400 transition-all duration-200",children:e},t)))})]})]},t)}))})]})})};var Or=o[" useId ".trim().toString()]||(()=>{}),Lr=0;function Mr(e){const[t,n]=r.useState(Or());return ue((()=>{e||n((e=>null!==e&&void 0!==e?e:String(Lr++)))}),[e]),e||(t?"radix-".concat(t):"")}const Dr=["loop","trapped","onMountAutoFocus","onUnmountAutoFocus"];var Ir="focusScope.autoFocusOnMount",Fr="focusScope.autoFocusOnUnmount",Hr={bubbles:!1,cancelable:!0},Ur=r.forwardRef(((e,t)=>{const{loop:n=!1,trapped:o=!1,onMountAutoFocus:a,onUnmountAutoFocus:l}=e,i=d(e,Dr),[s,c]=r.useState(null),f=X(a),p=X(l),m=r.useRef(null),h=E(t,(e=>c(e))),g=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect((()=>{if(o){let e=function(e){if(g.paused||!s)return;const t=e.target;s.contains(t)?m.current=t:$r(m.current,{select:!0})},t=function(e){if(g.paused||!s)return;const t=e.relatedTarget;null!==t&&(s.contains(t)||$r(m.current,{select:!0}))},n=function(e){if(document.activeElement===document.body)for(const t of e)t.removedNodes.length>0&&$r(s)};document.addEventListener("focusin",e),document.addEventListener("focusout",t);const r=new MutationObserver(n);return s&&r.observe(s,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),r.disconnect()}}}),[o,s,g.paused]),r.useEffect((()=>{if(s){qr.add(g);const t=document.activeElement;if(!s.contains(t)){const n=new CustomEvent(Ir,Hr);s.addEventListener(Ir,f),s.dispatchEvent(n),n.defaultPrevented||(!function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};const n=document.activeElement;for(const r of e)if($r(r,{select:t}),document.activeElement!==n)return}((e=Wr(s),e.filter((e=>"A"!==e.tagName))),{select:!0}),document.activeElement===t&&$r(s))}return()=>{s.removeEventListener(Ir,f),setTimeout((()=>{const e=new CustomEvent(Fr,Hr);s.addEventListener(Fr,p),s.dispatchEvent(e),e.defaultPrevented||$r(null!==t&&void 0!==t?t:document.body,{select:!0}),s.removeEventListener(Fr,p),qr.remove(g)}),0)}}var e}),[s,f,p,g]);const v=r.useCallback((e=>{if(!n&&!o)return;if(g.paused)return;const t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){const t=e.currentTarget,[o,a]=function(e){const t=Wr(e),n=Vr(t,e),r=Vr(t.reverse(),e);return[n,r]}(t);o&&a?e.shiftKey||r!==a?e.shiftKey&&r===o&&(e.preventDefault(),n&&$r(a,{select:!0})):(e.preventDefault(),n&&$r(o,{select:!0})):r===t&&e.preventDefault()}}),[n,o,g.paused]);return(0,T.jsx)(G.div,u(u({tabIndex:-1},i),{},{ref:h,onKeyDown:v}))}));function Wr(e){const t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{const t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function Vr(e,t){for(const n of e)if(!Br(n,{upTo:t}))return n}function Br(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e;){if(void 0!==n&&e===n)return!1;if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}function $r(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){const n=document.activeElement;e.focus({preventScroll:!0}),e!==n&&function(e){return e instanceof HTMLInputElement&&"select"in e}(e)&&t&&e.select()}}Ur.displayName="FocusScope";var qr=function(){let e=[];return{add(t){const n=e[0];t!==n&&(null===n||void 0===n||n.pause()),e=Kr(e,t),e.unshift(t)},remove(t){var n;e=Kr(e,t),null===(n=e[0])||void 0===n||n.resume()}}}();function Kr(e,t){const n=[...e],r=n.indexOf(t);return-1!==r&&n.splice(r,1),n}var Qr=0;function Gr(){r.useEffect((()=>{var e,t;const n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!==(e=n[0])&&void 0!==e?e:Yr()),document.body.insertAdjacentElement("beforeend",null!==(t=n[1])&&void 0!==t?t:Yr()),Qr++,()=>{1===Qr&&document.querySelectorAll("[data-radix-focus-guard]").forEach((e=>e.remove())),Qr--}}),[])}function Yr(){const e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}var Xr=function(){return Xr=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e},Xr.apply(this,arguments)};function Zr(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(null!=e&&"function"===typeof Object.getOwnPropertySymbols){var o=0;for(r=Object.getOwnPropertySymbols(e);o<r.length;o++)t.indexOf(r[o])<0&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]])}return n}Object.create;function Jr(e,t,n){if(n||2===arguments.length)for(var r,o=0,a=t.length;o<a;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))}Object.create;"function"===typeof SuppressedError&&SuppressedError;var eo="right-scroll-bar-position",to="width-before-scroll-bar";function no(e,t){return"function"===typeof e?e(t):e&&(e.current=t),e}var ro="undefined"!==typeof window?r.useLayoutEffect:r.useEffect,oo=new WeakMap;function ao(e,t){var n=function(e,t){var n=(0,r.useState)((function(){return{value:e,callback:t,facade:{get current(){return n.value},set current(e){var t=n.value;t!==e&&(n.value=e,n.callback(e,t))}}}}))[0];return n.callback=t,n.facade}(t||null,(function(t){return e.forEach((function(e){return no(e,t)}))}));return ro((function(){var t=oo.get(n);if(t){var r=new Set(t),o=new Set(e),a=n.current;r.forEach((function(e){o.has(e)||no(e,null)})),o.forEach((function(e){r.has(e)||no(e,a)}))}oo.set(n,e)}),[e]),n}function lo(e){return e}function io(e,t){void 0===t&&(t=lo);var n=[],r=!1;return{read:function(){if(r)throw new Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:e},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter((function(e){return e!==o}))}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var a=function(){var n=t;t=[],n.forEach(e)},l=function(){return Promise.resolve().then(a)};l(),n={push:function(e){t.push(e),l()},filter:function(e){return t=t.filter(e),n}}}}}var so=function(e){void 0===e&&(e={});var t=io(null);return t.options=Xr({async:!0,ssr:!1},e),t}(),co=function(){},uo=r.forwardRef((function(e,t){var n=r.useRef(null),o=r.useState({onScrollCapture:co,onWheelCapture:co,onTouchMoveCapture:co}),a=o[0],l=o[1],i=e.forwardProps,s=e.children,c=e.className,u=e.removeScrollBar,d=e.enabled,f=e.shards,p=e.sideCar,m=e.noIsolation,h=e.inert,g=e.allowPinchZoom,v=e.as,y=void 0===v?"div":v,b=e.gapMode,w=Zr(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),x=p,k=ao([n,t]),S=Xr(Xr({},w),a);return r.createElement(r.Fragment,null,d&&r.createElement(x,{sideCar:so,removeScrollBar:u,shards:f,noIsolation:m,inert:h,setCallbacks:l,allowPinchZoom:!!g,lockRef:n,gapMode:b}),i?r.cloneElement(r.Children.only(s),Xr(Xr({},S),{ref:k})):r.createElement(y,Xr({},S,{className:c,ref:k}),s))}));uo.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},uo.classNames={fullWidth:to,zeroRight:eo};var fo,po=function(e){var t=e.sideCar,n=Zr(e,["sideCar"]);if(!t)throw new Error("Sidecar: please provide `sideCar` property to import the right car");var o=t.read();if(!o)throw new Error("Sidecar medium not found");return r.createElement(o,Xr({},n))};po.isSideCarExport=!0;function mo(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=fo||n.nc;return t&&e.setAttribute("nonce",t),e}var ho=function(){var e=0,t=null;return{add:function(n){var r,o;0==e&&(t=mo())&&(o=n,(r=t).styleSheet?r.styleSheet.cssText=o:r.appendChild(document.createTextNode(o)),function(e){(document.head||document.getElementsByTagName("head")[0]).appendChild(e)}(t)),e++},remove:function(){! --e&&t&&(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},go=function(){var e=function(){var e=ho();return function(t,n){r.useEffect((function(){return e.add(t),function(){e.remove()}}),[t&&n])}}();return function(t){var n=t.styles,r=t.dynamic;return e(n,r),null}},vo={left:0,top:0,right:0,gap:0},yo=function(e){return parseInt(e||"",10)||0},bo=function(e){if(void 0===e&&(e="margin"),"undefined"===typeof window)return vo;var t=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[yo(n),yo(r),yo(o)]}(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},wo=go(),xo="data-scroll-locked",ko=function(e,t,n,r){var o=e.left,a=e.top,l=e.right,i=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(i,"px ").concat(r,";\n  }\n  body[").concat(xo,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(a,"px;\n    padding-right: ").concat(l,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(i,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(i,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(eo," {\n    right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(to," {\n    margin-right: ").concat(i,"px ").concat(r,";\n  }\n  \n  .").concat(eo," .").concat(eo," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(to," .").concat(to," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(xo,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(i,"px;\n  }\n")},So=function(){var e=parseInt(document.body.getAttribute(xo)||"0",10);return isFinite(e)?e:0},No=function(e){var t=e.noRelative,n=e.noImportant,o=e.gapMode,a=void 0===o?"margin":o;r.useEffect((function(){return document.body.setAttribute(xo,(So()+1).toString()),function(){var e=So()-1;e<=0?document.body.removeAttribute(xo):document.body.setAttribute(xo,e.toString())}}),[]);var l=r.useMemo((function(){return bo(a)}),[a]);return r.createElement(wo,{styles:ko(l,!t,a,n?"":"!important")})},Eo=!1;if("undefined"!==typeof window)try{var Co=Object.defineProperty({},"passive",{get:function(){return Eo=!0,!0}});window.addEventListener("test",Co,Co),window.removeEventListener("test",Co,Co)}catch(Su){Eo=!1}var jo=!!Eo&&{passive:!1},Po=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&!function(e){return"TEXTAREA"===e.tagName}(e)&&"visible"===n[t])},_o=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!==typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),To(e,r)){var o=Ro(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},To=function(e,t){return"v"===e?function(e){return Po(e,"overflowY")}(t):function(e){return Po(e,"overflowX")}(t)},Ro=function(e,t){return"v"===e?[(n=t).scrollTop,n.scrollHeight,n.clientHeight]:function(e){return[e.scrollLeft,e.scrollWidth,e.clientWidth]}(t);var n},Ao=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},zo=function(e){return[e.deltaX,e.deltaY]},Oo=function(e){return e&&"current"in e?e.current:e},Lo=function(e){return"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")},Mo=0,Do=[];function Io(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}const Fo=(Ho=function(e){var t=r.useRef([]),n=r.useRef([0,0]),o=r.useRef(),a=r.useState(Mo++)[0],l=r.useState(go)[0],i=r.useRef(e);r.useEffect((function(){i.current=e}),[e]),r.useEffect((function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(a));var t=Jr([e.lockRef.current],(e.shards||[]).map(Oo),!0).filter(Boolean);return t.forEach((function(e){return e.classList.add("allow-interactivity-".concat(a))})),function(){document.body.classList.remove("block-interactivity-".concat(a)),t.forEach((function(e){return e.classList.remove("allow-interactivity-".concat(a))}))}}}),[e.inert,e.lockRef.current,e.shards]);var s=r.useCallback((function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!i.current.allowPinchZoom;var r,a=Ao(e),l=n.current,s="deltaX"in e?e.deltaX:l[0]-a[0],c="deltaY"in e?e.deltaY:l[1]-a[1],u=e.target,d=Math.abs(s)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===d&&"range"===u.type)return!1;var f=_o(d,u);if(!f)return!0;if(f?r=d:(r="v"===d?"h":"v",f=_o(d,u)),!f)return!1;if(!o.current&&"changedTouches"in e&&(s||c)&&(o.current=r),!r)return!0;var p=o.current||r;return function(e,t,n,r,o){var a=function(e,t){return"h"===e&&"rtl"===t?-1:1}(e,window.getComputedStyle(t).direction),l=a*r,i=n.target,s=t.contains(i),c=!1,u=l>0,d=0,f=0;do{var p=Ro(e,i),m=p[0],h=p[1]-p[2]-a*m;(m||h)&&To(e,i)&&(d+=h,f+=m),i=i instanceof ShadowRoot?i.host:i.parentNode}while(!s&&i!==document.body||s&&(t.contains(i)||t===i));return(u&&(o&&Math.abs(d)<1||!o&&l>d)||!u&&(o&&Math.abs(f)<1||!o&&-l>f))&&(c=!0),c}(p,t,e,"h"===p?s:c,!0)}),[]),c=r.useCallback((function(e){var n=e;if(Do.length&&Do[Do.length-1]===l){var r="deltaY"in n?zo(n):Ao(n),o=t.current.filter((function(e){return e.name===n.type&&(e.target===n.target||n.target===e.shadowParent)&&(t=e.delta,o=r,t[0]===o[0]&&t[1]===o[1]);var t,o}))[0];if(o&&o.should)n.cancelable&&n.preventDefault();else if(!o){var a=(i.current.shards||[]).map(Oo).filter(Boolean).filter((function(e){return e.contains(n.target)}));(a.length>0?s(n,a[0]):!i.current.noIsolation)&&n.cancelable&&n.preventDefault()}}}),[]),u=r.useCallback((function(e,n,r,o){var a={name:e,delta:n,target:r,should:o,shadowParent:Io(r)};t.current.push(a),setTimeout((function(){t.current=t.current.filter((function(e){return e!==a}))}),1)}),[]),d=r.useCallback((function(e){n.current=Ao(e),o.current=void 0}),[]),f=r.useCallback((function(t){u(t.type,zo(t),t.target,s(t,e.lockRef.current))}),[]),p=r.useCallback((function(t){u(t.type,Ao(t),t.target,s(t,e.lockRef.current))}),[]);r.useEffect((function(){return Do.push(l),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",c,jo),document.addEventListener("touchmove",c,jo),document.addEventListener("touchstart",d,jo),function(){Do=Do.filter((function(e){return e!==l})),document.removeEventListener("wheel",c,jo),document.removeEventListener("touchmove",c,jo),document.removeEventListener("touchstart",d,jo)}}),[]);var m=e.removeScrollBar,h=e.inert;return r.createElement(r.Fragment,null,h?r.createElement(l,{styles:Lo(a)}):null,m?r.createElement(No,{gapMode:e.gapMode}):null)},so.useMedium(Ho),po);var Ho,Uo=r.forwardRef((function(e,t){return r.createElement(uo,Xr({},e,{ref:t,sideCar:Fo}))}));Uo.classNames=uo.classNames;const Wo=Uo;var Vo=function(e){return"undefined"===typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body},Bo=new WeakMap,$o=new WeakMap,qo={},Ko=0,Qo=function(e){return e&&(e.host||Qo(e.parentNode))},Go=function(e,t,n,r){var o=function(e,t){return t.map((function(t){if(e.contains(t))return t;var n=Qo(t);return n&&e.contains(n)?n:(console.error("aria-hidden",t,"in not contained inside",e,". Doing nothing"),null)})).filter((function(e){return Boolean(e)}))}(t,Array.isArray(e)?e:[e]);qo[n]||(qo[n]=new WeakMap);var a=qo[n],l=[],i=new Set,s=new Set(o),c=function(e){e&&!i.has(e)&&(i.add(e),c(e.parentNode))};o.forEach(c);var u=function(e){e&&!s.has(e)&&Array.prototype.forEach.call(e.children,(function(e){if(i.has(e))u(e);else try{var t=e.getAttribute(r),o=null!==t&&"false"!==t,s=(Bo.get(e)||0)+1,c=(a.get(e)||0)+1;Bo.set(e,s),a.set(e,c),l.push(e),1===s&&o&&$o.set(e,!0),1===c&&e.setAttribute(n,"true"),o||e.setAttribute(r,"true")}catch(d){console.error("aria-hidden: cannot operate on ",e,d)}}))};return u(t),i.clear(),Ko++,function(){l.forEach((function(e){var t=Bo.get(e)-1,o=a.get(e)-1;Bo.set(e,t),a.set(e,o),t||($o.has(e)||e.removeAttribute(r),$o.delete(e)),o||e.removeAttribute(n)})),--Ko||(Bo=new WeakMap,Bo=new WeakMap,$o=new WeakMap,qo={})}},Yo=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||Vo(e);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),Go(r,o,n,"aria-hidden")):function(){return null}};const Xo=["__scopeDialog"],Zo=["forceMount"],Jo=["__scopeDialog"],ea=["forceMount"],ta=["__scopeDialog","trapFocus","onOpenAutoFocus","onCloseAutoFocus"],na=["__scopeDialog"],ra=["__scopeDialog"],oa=["__scopeDialog"];var aa="Dialog",[la,ia]=z(aa),[sa,ca]=la(aa),ua=e=>{const{__scopeDialog:t,children:n,open:o,defaultOpen:a,onOpenChange:l,modal:i=!0}=e,s=r.useRef(null),c=r.useRef(null),[u,d]=ge({prop:o,defaultProp:null!==a&&void 0!==a&&a,onChange:l,caller:aa});return(0,T.jsx)(sa,{scope:t,triggerRef:s,contentRef:c,contentId:Mr(),titleId:Mr(),descriptionId:Mr(),open:u,onOpenChange:d,onOpenToggle:r.useCallback((()=>d((e=>!e))),[d]),modal:i,children:n})};ua.displayName=aa;var da="DialogTrigger",fa=r.forwardRef(((e,t)=>{const{__scopeDialog:n}=e,r=d(e,Xo),o=ca(da,n),a=E(t,o.triggerRef);return(0,T.jsx)(G.button,u(u({type:"button","aria-haspopup":"dialog","aria-expanded":o.open,"aria-controls":o.contentId,"data-state":Aa(o.open)},r),{},{ref:a,onClick:k(e.onClick,o.onOpenToggle)}))}));fa.displayName=da;var pa="DialogPortal",[ma,ha]=la(pa,{forceMount:void 0}),ga=e=>{const{__scopeDialog:t,forceMount:n,children:o,container:a}=e,l=ca(pa,t);return(0,T.jsx)(ma,{scope:t,forceMount:n,children:r.Children.map(o,(e=>(0,T.jsx)(pe,{present:n||l.open,children:(0,T.jsx)(fe,{asChild:!0,container:a,children:e})})))})};ga.displayName=pa;var va="DialogOverlay",ya=r.forwardRef(((e,t)=>{const n=ha(va,e.__scopeDialog),{forceMount:r=n.forceMount}=e,o=d(e,Zo),a=ca(va,e.__scopeDialog);return a.modal?(0,T.jsx)(pe,{present:r||a.open,children:(0,T.jsx)(wa,u(u({},o),{},{ref:t}))}):null}));ya.displayName=va;var ba=D("DialogOverlay.RemoveScroll"),wa=r.forwardRef(((e,t)=>{const{__scopeDialog:n}=e,r=d(e,Jo),o=ca(va,n);return(0,T.jsx)(Wo,{as:ba,allowPinchZoom:!0,shards:[o.contentRef],children:(0,T.jsx)(G.div,u(u({"data-state":Aa(o.open)},r),{},{ref:t,style:u({pointerEvents:"auto"},r.style)}))})})),xa="DialogContent",ka=r.forwardRef(((e,t)=>{const n=ha(xa,e.__scopeDialog),{forceMount:r=n.forceMount}=e,o=d(e,ea),a=ca(xa,e.__scopeDialog);return(0,T.jsx)(pe,{present:r||a.open,children:a.modal?(0,T.jsx)(Sa,u(u({},o),{},{ref:t})):(0,T.jsx)(Na,u(u({},o),{},{ref:t}))})}));ka.displayName=xa;var Sa=r.forwardRef(((e,t)=>{const n=ca(xa,e.__scopeDialog),o=r.useRef(null),a=E(t,n.contentRef,o);return r.useEffect((()=>{const e=o.current;if(e)return Yo(e)}),[]),(0,T.jsx)(Ea,u(u({},e),{},{ref:a,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:k(e.onCloseAutoFocus,(e=>{var t;e.preventDefault(),null===(t=n.triggerRef.current)||void 0===t||t.focus()})),onPointerDownOutside:k(e.onPointerDownOutside,(e=>{const t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()})),onFocusOutside:k(e.onFocusOutside,(e=>e.preventDefault()))}))})),Na=r.forwardRef(((e,t)=>{const n=ca(xa,e.__scopeDialog),o=r.useRef(!1),a=r.useRef(!1);return(0,T.jsx)(Ea,u(u({},e),{},{ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,l;(null===(r=e.onCloseAutoFocus)||void 0===r||r.call(e,t),t.defaultPrevented)||(o.current||null===(l=n.triggerRef.current)||void 0===l||l.focus(),t.preventDefault());o.current=!1,a.current=!1},onInteractOutside:t=>{var r,l;null===(r=e.onInteractOutside)||void 0===r||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(a.current=!0));const i=t.target;(null===(l=n.triggerRef.current)||void 0===l?void 0:l.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&a.current&&t.preventDefault()}}))})),Ea=r.forwardRef(((e,t)=>{const{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l}=e,i=d(e,ta),s=ca(xa,n),c=r.useRef(null),f=E(t,c);return Gr(),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(Ur,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,T.jsx)(oe,u(u({role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":Aa(s.open)},i),{},{ref:f,onDismiss:()=>s.onOpenChange(!1)}))}),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(Ma,{titleId:s.titleId}),(0,T.jsx)(Da,{contentRef:c,descriptionId:s.descriptionId})]})]})})),Ca="DialogTitle",ja=r.forwardRef(((e,t)=>{const{__scopeDialog:n}=e,r=d(e,na),o=ca(Ca,n);return(0,T.jsx)(G.h2,u(u({id:o.titleId},r),{},{ref:t}))}));ja.displayName=Ca;var Pa="DialogDescription",_a=r.forwardRef(((e,t)=>{const{__scopeDialog:n}=e,r=d(e,ra),o=ca(Pa,n);return(0,T.jsx)(G.p,u(u({id:o.descriptionId},r),{},{ref:t}))}));_a.displayName=Pa;var Ta="DialogClose",Ra=r.forwardRef(((e,t)=>{const{__scopeDialog:n}=e,r=d(e,oa),o=ca(Ta,n);return(0,T.jsx)(G.button,u(u({type:"button"},r),{},{ref:t,onClick:k(e.onClick,(()=>o.onOpenChange(!1)))}))}));function Aa(e){return e?"open":"closed"}Ra.displayName=Ta;var za="DialogTitleWarning",[Oa,La]=function(e,t){const n=r.createContext(t),o=e=>{const{children:t}=e,o=d(e,R),a=r.useMemo((()=>o),Object.values(o));return(0,T.jsx)(n.Provider,{value:a,children:t})};return o.displayName=e+"Provider",[o,function(o){const a=r.useContext(n);if(a)return a;if(void 0!==t)return t;throw new Error("`".concat(o,"` must be used within `").concat(e,"`"))}]}(za,{contentName:xa,titleName:Ca,docsSlug:"dialog"}),Ma=e=>{let{titleId:t}=e;const n=La(za),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect((()=>{if(t){document.getElementById(t)||console.error(o)}}),[o,t]),null},Da=e=>{let{contentRef:t,descriptionId:n}=e;const o=La("DialogDescriptionWarning"),a="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect((()=>{var e;const r=null===(e=t.current)||void 0===e?void 0:e.getAttribute("aria-describedby");if(n&&r){document.getElementById(n)||console.warn(a)}}),[a,t,n]),null},Ia=ya,Fa=ka,Ha=ja,Ua=_a,Wa=Ra;const Va=["className"],Ba=["className","children"],$a=["className"],qa=["className"],Ka=["className"],Qa=ua,Ga=ga,Ya=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,Va);return(0,T.jsx)(Ia,u({ref:t,className:In("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",n)},r))}));Ya.displayName=Ia.displayName;const Xa=r.forwardRef(((e,t)=>{let{className:n,children:r}=e,o=d(e,Ba);return(0,T.jsxs)(Ga,{children:[(0,T.jsx)(Ya,{}),(0,T.jsxs)(Fa,u(u({ref:t,className:In("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",n)},o),{},{children:[r,(0,T.jsxs)(Wa,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,T.jsx)(_t,{className:"h-4 w-4"}),(0,T.jsx)("span",{className:"sr-only",children:"Close"})]})]}))]})}));Xa.displayName=Fa.displayName;const Za=e=>{let{className:t}=e,n=d(e,$a);return(0,T.jsx)("div",u({className:In("flex flex-col space-y-1.5 text-center sm:text-left",t)},n))};Za.displayName="DialogHeader";const Ja=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,qa);return(0,T.jsx)(Ha,u({ref:t,className:In("text-lg font-semibold leading-none tracking-tight",n)},r))}));Ja.displayName=Ha.displayName;r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,Ka);return(0,T.jsx)(Ua,u({ref:t,className:In("text-sm text-muted-foreground",n)},r))})).displayName=Ua.displayName;const el=Pt("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),tl=Pt("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),nl=()=>{const[e,t]=(0,r.useState)("All"),[n,o]=(0,r.useState)(null),a=[{id:1,title:"LuminaIQ",category:"EdTech",description:"AI-powered learning platform with adaptive question generation and real-time performance evaluation.",image:"https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=400&fit=crop",tags:["AI","Education","React","Python"],highlights:["Adaptive Quiz Generation","Real-time Feedback","Gamified Progress","Multi-language Support"],metrics:{users:"10K+",accuracy:"94%",engagement:"85%"},demoUrl:"#",caseStudyUrl:"#"},{id:2,title:"CRM Pro Suite",category:"Business",description:"Comprehensive CRM solution with AI-powered analytics, billing management, and investment tracking.",image:"https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=600&h=400&fit=crop",tags:["CRM","Analytics","Dashboard","AI"],highlights:["Drag-Drop Reporting","Role-based Permissions","Audit Logs","Predictive Analytics"],metrics:{efficiency:"60%",satisfaction:"98%",roi:"300%"},demoUrl:"#",caseStudyUrl:"#"},{id:3,title:"DataForge ML",category:"Data Science",description:"End-to-end machine learning pipeline with automated model selection and deployment capabilities.",image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",tags:["ML","Python","Docker","Kubernetes"],highlights:["AutoML Capabilities","Model Deployment","Data Visualization","API Integration"],metrics:{accuracy:"96%",speed:"10x",scalability:"\u221e"},demoUrl:"#",caseStudyUrl:"#"},{id:4,title:"Tabble Manager",category:"Mobile",description:"Sensor-driven hotel table management system with real-time occupancy tracking and analytics.",image:"https://images.unsplash.com/photo-1414235077428-338989a2e8c0?w=600&h=400&fit=crop",tags:["React Native","IoT","Real-time","Analytics"],highlights:["Sensor Integration","Real-time Updates","Offline Sync","Analytics Dashboard"],metrics:{efficiency:"40%",accuracy:"99%",uptime:"99.9%"},demoUrl:"#",caseStudyUrl:"#"},{id:5,title:"Zen Analyzer Pro",category:"Data Analysis",description:"AI-powered data analysis tool that transforms CSV/Excel files into interactive insights and visualizations.",image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=600&h=400&fit=crop",tags:["AI","Analytics","Visualization","NLP"],highlights:["Natural Language Queries","Auto-Visualization","Export Capabilities","Multi-format Support"],metrics:{speed:"50x",accuracy:"95%",satisfaction:"96%"},demoUrl:"#",caseStudyUrl:"#"},{id:6,title:"EduTech Suite",category:"Education",description:"Comprehensive educational platform with anonymous feedback system and AI-powered learning analytics.",image:"https://images.unsplash.com/photo-1503676260728-1c00da094a0b?w=600&h=400&fit=crop",tags:["Education","React","AI","Analytics"],highlights:["Anonymous Feedback","Learning Analytics","Progress Tracking","Multi-tenant Support"],metrics:{adoption:"89%",engagement:"76%",retention:"92%"},demoUrl:"#",caseStudyUrl:"#"}],l="All"===e?a:a.filter((t=>t.category===e));return(0,T.jsx)("section",{id:"projects",className:"py-20 bg-gradient-to-b from-black to-gray-900",children:(0,T.jsxs)("div",{className:"container mx-auto px-6",children:[(0,T.jsxs)("div",{className:"text-center mb-16",children:[(0,T.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold mb-6",children:[(0,T.jsx)("span",{className:"bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent",children:"Our"}),(0,T.jsx)("span",{className:"block bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent",children:"Projects"})]}),(0,T.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto mb-8",children:"Explore our portfolio of cutting-edge AI solutions that have transformed businesses across industries."}),(0,T.jsx)("div",{className:"flex flex-wrap justify-center gap-3",children:["All","EdTech","Business","Data Science","Mobile","Data Analysis","Education"].map((n=>(0,T.jsx)(nr,{variant:e===n?"default":"outline",onClick:()=>t(n),className:e===n?"bg-gradient-to-r from-yellow-400 to-yellow-600 text-black hover:from-yellow-300 hover:to-yellow-500":"border-gray-700 text-gray-300 hover:bg-gray-800/50 hover:text-yellow-400 hover:border-yellow-400/30",children:n},n)))})]}),(0,T.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-8",children:l.map((e=>(0,T.jsxs)(vr,{className:"group bg-gray-900/50 backdrop-blur-sm border-gray-800/50 overflow-hidden hover:border-yellow-400/30 transition-all duration-500 hover:-translate-y-2 hover:shadow-lg hover:shadow-yellow-400/10",children:[(0,T.jsxs)("div",{className:"relative overflow-hidden",children:[(0,T.jsx)("img",{src:e.image,alt:e.title,className:"w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"}),(0,T.jsx)("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300",children:(0,T.jsx)("div",{className:"absolute bottom-4 left-4 right-4",children:(0,T.jsxs)("div",{className:"flex gap-2",children:[(0,T.jsxs)(nr,{size:"sm",className:"bg-yellow-400/90 text-black hover:bg-yellow-300",onClick:()=>o(e),children:[(0,T.jsx)(el,{className:"w-4 h-4 mr-2"}),"View Details"]}),(0,T.jsxs)(nr,{size:"sm",variant:"outline",className:"border-white/30 text-white hover:bg-white/20",children:[(0,T.jsx)(tl,{className:"w-4 h-4 mr-2"}),"Demo"]})]})})})]}),(0,T.jsxs)(yr,{children:[(0,T.jsx)("div",{className:"flex items-center justify-between mb-2",children:(0,T.jsx)(Er,{variant:"outline",className:"bg-yellow-400/10 border-yellow-400/30 text-yellow-400",children:e.category})}),(0,T.jsx)(br,{className:"text-xl font-semibold text-white group-hover:text-yellow-400 transition-colors duration-300",children:e.title})]}),(0,T.jsxs)(wr,{className:"pt-0",children:[(0,T.jsx)("p",{className:"text-gray-400 text-sm leading-relaxed mb-4",children:e.description}),(0,T.jsx)("div",{className:"flex flex-wrap gap-2",children:e.tags.map(((e,t)=>(0,T.jsx)(Er,{variant:"outline",className:"text-xs bg-gray-800/50 border-gray-700/50 text-gray-300",children:e},t)))})]})]},e.id)))}),n&&(0,T.jsx)(Qa,{open:!!n,onOpenChange:()=>o(null),children:(0,T.jsxs)(Xa,{className:"max-w-4xl bg-gray-900 border-gray-800 text-white",children:[(0,T.jsx)(Za,{children:(0,T.jsx)(Ja,{className:"text-2xl font-bold text-yellow-400",children:n.title})}),(0,T.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,T.jsxs)("div",{children:[(0,T.jsx)("img",{src:n.image,alt:n.title,className:"w-full h-64 object-cover rounded-lg mb-4"}),(0,T.jsxs)("div",{className:"space-y-4",children:[(0,T.jsxs)("div",{children:[(0,T.jsx)("h4",{className:"text-lg font-semibold text-yellow-400 mb-2",children:"Key Highlights"}),(0,T.jsx)("ul",{className:"space-y-1",children:n.highlights.map(((e,t)=>(0,T.jsxs)("li",{className:"text-gray-300 text-sm",children:["\u2022 ",e]},t)))})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("h4",{className:"text-lg font-semibold text-yellow-400 mb-2",children:"Metrics"}),(0,T.jsx)("div",{className:"grid grid-cols-2 gap-4",children:Object.entries(n.metrics).map((e=>{let[t,n]=e;return(0,T.jsxs)("div",{className:"text-center p-3 bg-gray-800/50 rounded-lg",children:[(0,T.jsx)("div",{className:"text-2xl font-bold text-yellow-400",children:n}),(0,T.jsx)("div",{className:"text-xs text-gray-400 capitalize",children:t})]},t)}))})]})]})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("p",{className:"text-gray-300 leading-relaxed mb-6",children:n.description}),(0,T.jsxs)("div",{className:"mb-6",children:[(0,T.jsx)("h4",{className:"text-lg font-semibold text-yellow-400 mb-3",children:"Technology Stack"}),(0,T.jsx)("div",{className:"flex flex-wrap gap-2",children:n.tags.map(((e,t)=>(0,T.jsx)(Er,{className:"bg-yellow-400/10 border-yellow-400/30 text-yellow-400",children:e},t)))})]}),(0,T.jsxs)("div",{className:"flex gap-3",children:[(0,T.jsxs)(nr,{className:"bg-gradient-to-r from-yellow-400 to-yellow-600 text-black hover:from-yellow-300 hover:to-yellow-500",children:[(0,T.jsx)(tl,{className:"w-4 h-4 mr-2"}),"View Demo"]}),(0,T.jsx)(nr,{variant:"outline",className:"border-gray-700 text-gray-300 hover:bg-gray-800/50",children:"Case Study"})]})]})]})]})})]})})},rl=["className","type"],ol=r.forwardRef(((e,t)=>{let{className:n,type:r}=e,o=d(e,rl);return(0,T.jsx)("input",u({type:r,className:In("flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-base shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",n),ref:t},o))}));ol.displayName="Input";const al=["className"],ll=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,al);return(0,T.jsx)("textarea",u({className:In("flex min-h-[60px] w-full rounded-md border border-input bg-transparent px-3 py-2 text-base shadow-sm placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",n),ref:t},r))}));function il(e,t){let[n,r]=t;return Math.min(r,Math.max(n,e))}ll.displayName="Textarea";var sl=r.createContext(void 0);const cl=["top","right","bottom","left"],ul=Math.min,dl=Math.max,fl=Math.round,pl=Math.floor,ml=e=>({x:e,y:e}),hl={left:"right",right:"left",bottom:"top",top:"bottom"},gl={start:"end",end:"start"};function vl(e,t,n){return dl(e,ul(t,n))}function yl(e,t){return"function"===typeof e?e(t):e}function bl(e){return e.split("-")[0]}function wl(e){return e.split("-")[1]}function xl(e){return"x"===e?"y":"x"}function kl(e){return"y"===e?"height":"width"}function Sl(e){return["top","bottom"].includes(bl(e))?"y":"x"}function Nl(e){return xl(Sl(e))}function El(e){return e.replace(/start|end/g,(e=>gl[e]))}function Cl(e){return e.replace(/left|right|bottom|top/g,(e=>hl[e]))}function jl(e){return"number"!==typeof e?function(e){return u({top:0,right:0,bottom:0,left:0},e)}(e):{top:e,right:e,bottom:e,left:e}}function Pl(e){const{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}const _l=["mainAxis","crossAxis","fallbackPlacements","fallbackStrategy","fallbackAxisSideDirection","flipAlignment"],Tl=["strategy"],Rl=["mainAxis","crossAxis","limiter"],Al=["apply"];function zl(e,t,n){let{reference:r,floating:o}=e;const a=Sl(t),l=Nl(t),i=kl(l),s=bl(t),c="y"===a,u=r.x+r.width/2-o.width/2,d=r.y+r.height/2-o.height/2,f=r[i]/2-o[i]/2;let p;switch(s){case"top":p={x:u,y:r.y-o.height};break;case"bottom":p={x:u,y:r.y+r.height};break;case"right":p={x:r.x+r.width,y:d};break;case"left":p={x:r.x-o.width,y:d};break;default:p={x:r.x,y:r.y}}switch(wl(t)){case"start":p[l]-=f*(n&&c?-1:1);break;case"end":p[l]+=f*(n&&c?-1:1)}return p}async function Ol(e,t){var n;void 0===t&&(t={});const{x:r,y:o,platform:a,rects:l,elements:i,strategy:s}=e,{boundary:c="clippingAncestors",rootBoundary:u="viewport",elementContext:d="floating",altBoundary:f=!1,padding:p=0}=yl(t,e),m=jl(p),h=i[f?"floating"===d?"reference":"floating":d],g=Pl(await a.getClippingRect({element:null==(n=await(null==a.isElement?void 0:a.isElement(h)))||n?h:h.contextElement||await(null==a.getDocumentElement?void 0:a.getDocumentElement(i.floating)),boundary:c,rootBoundary:u,strategy:s})),v="floating"===d?{x:r,y:o,width:l.floating.width,height:l.floating.height}:l.reference,y=await(null==a.getOffsetParent?void 0:a.getOffsetParent(i.floating)),b=await(null==a.isElement?void 0:a.isElement(y))&&await(null==a.getScale?void 0:a.getScale(y))||{x:1,y:1},w=Pl(a.convertOffsetParentRelativeRectToViewportRelativeRect?await a.convertOffsetParentRelativeRectToViewportRelativeRect({elements:i,rect:v,offsetParent:y,strategy:s}):v);return{top:(g.top-w.top+m.top)/b.y,bottom:(w.bottom-g.bottom+m.bottom)/b.y,left:(g.left-w.left+m.left)/b.x,right:(w.right-g.right+m.right)/b.x}}function Ll(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function Ml(e){return cl.some((t=>e[t]>=0))}function Dl(){return"undefined"!==typeof window}function Il(e){return Ul(e)?(e.nodeName||"").toLowerCase():"#document"}function Fl(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function Hl(e){var t;return null==(t=(Ul(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function Ul(e){return!!Dl()&&(e instanceof Node||e instanceof Fl(e).Node)}function Wl(e){return!!Dl()&&(e instanceof Element||e instanceof Fl(e).Element)}function Vl(e){return!!Dl()&&(e instanceof HTMLElement||e instanceof Fl(e).HTMLElement)}function Bl(e){return!(!Dl()||"undefined"===typeof ShadowRoot)&&(e instanceof ShadowRoot||e instanceof Fl(e).ShadowRoot)}function $l(e){const{overflow:t,overflowX:n,overflowY:r,display:o}=Xl(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function ql(e){return["table","td","th"].includes(Il(e))}function Kl(e){return[":popover-open",":modal"].some((t=>{try{return e.matches(t)}catch(n){return!1}}))}function Ql(e){const t=Gl(),n=Wl(e)?Xl(e):e;return["transform","translate","scale","rotate","perspective"].some((e=>!!n[e]&&"none"!==n[e]))||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","translate","scale","rotate","perspective","filter"].some((e=>(n.willChange||"").includes(e)))||["paint","layout","strict","content"].some((e=>(n.contain||"").includes(e)))}function Gl(){return!("undefined"===typeof CSS||!CSS.supports)&&CSS.supports("-webkit-backdrop-filter","none")}function Yl(e){return["html","body","#document"].includes(Il(e))}function Xl(e){return Fl(e).getComputedStyle(e)}function Zl(e){return Wl(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function Jl(e){if("html"===Il(e))return e;const t=e.assignedSlot||e.parentNode||Bl(e)&&e.host||Hl(e);return Bl(t)?t.host:t}function ei(e){const t=Jl(e);return Yl(t)?e.ownerDocument?e.ownerDocument.body:e.body:Vl(t)&&$l(t)?t:ei(t)}function ti(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);const o=ei(e),a=o===(null==(r=e.ownerDocument)?void 0:r.body),l=Fl(o);if(a){const e=ni(l);return t.concat(l,l.visualViewport||[],$l(o)?o:[],e&&n?ti(e):[])}return t.concat(o,ti(o,[],n))}function ni(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function ri(e){const t=Xl(e);let n=parseFloat(t.width)||0,r=parseFloat(t.height)||0;const o=Vl(e),a=o?e.offsetWidth:n,l=o?e.offsetHeight:r,i=fl(n)!==a||fl(r)!==l;return i&&(n=a,r=l),{width:n,height:r,$:i}}function oi(e){return Wl(e)?e:e.contextElement}function ai(e){const t=oi(e);if(!Vl(t))return ml(1);const n=t.getBoundingClientRect(),{width:r,height:o,$:a}=ri(t);let l=(a?fl(n.width):n.width)/r,i=(a?fl(n.height):n.height)/o;return l&&Number.isFinite(l)||(l=1),i&&Number.isFinite(i)||(i=1),{x:l,y:i}}const li=ml(0);function ii(e){const t=Fl(e);return Gl()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:li}function si(e,t,n,r){void 0===t&&(t=!1),void 0===n&&(n=!1);const o=e.getBoundingClientRect(),a=oi(e);let l=ml(1);t&&(r?Wl(r)&&(l=ai(r)):l=ai(e));const i=function(e,t,n){return void 0===t&&(t=!1),!(!n||t&&n!==Fl(e))&&t}(a,n,r)?ii(a):ml(0);let s=(o.left+i.x)/l.x,c=(o.top+i.y)/l.y,u=o.width/l.x,d=o.height/l.y;if(a){const e=Fl(a),t=r&&Wl(r)?Fl(r):r;let n=e,o=ni(n);for(;o&&r&&t!==n;){const e=ai(o),t=o.getBoundingClientRect(),r=Xl(o),a=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,l=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,c*=e.y,u*=e.x,d*=e.y,s+=a,c+=l,n=Fl(o),o=ni(n)}}return Pl({width:u,height:d,x:s,y:c})}function ci(e,t){const n=Zl(e).scrollLeft;return t?t.left+n:si(Hl(e)).left+n}function ui(e,t,n){void 0===n&&(n=!1);const r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ci(e,r)),y:r.top+t.scrollTop}}function di(e,t,n){let r;if("viewport"===t)r=function(e,t){const n=Fl(e),r=Hl(e),o=n.visualViewport;let a=r.clientWidth,l=r.clientHeight,i=0,s=0;if(o){a=o.width,l=o.height;const e=Gl();(!e||e&&"fixed"===t)&&(i=o.offsetLeft,s=o.offsetTop)}return{width:a,height:l,x:i,y:s}}(e,n);else if("document"===t)r=function(e){const t=Hl(e),n=Zl(e),r=e.ownerDocument.body,o=dl(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=dl(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight);let l=-n.scrollLeft+ci(e);const i=-n.scrollTop;return"rtl"===Xl(r).direction&&(l+=dl(t.clientWidth,r.clientWidth)-o),{width:o,height:a,x:l,y:i}}(Hl(e));else if(Wl(t))r=function(e,t){const n=si(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,a=Vl(e)?ai(e):ml(1);return{width:e.clientWidth*a.x,height:e.clientHeight*a.y,x:o*a.x,y:r*a.y}}(t,n);else{const n=ii(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return Pl(r)}function fi(e,t){const n=Jl(e);return!(n===t||!Wl(n)||Yl(n))&&("fixed"===Xl(n).position||fi(n,t))}function pi(e,t,n){const r=Vl(t),o=Hl(t),a="fixed"===n,l=si(e,!0,a,t);let i={scrollLeft:0,scrollTop:0};const s=ml(0);function c(){s.x=ci(o)}if(r||!r&&!a)if(("body"!==Il(t)||$l(o))&&(i=Zl(t)),r){const e=si(t,!0,a,t);s.x=e.x+t.clientLeft,s.y=e.y+t.clientTop}else o&&c();a&&!r&&o&&c();const u=!o||r||a?ml(0):ui(o,i);return{x:l.left+i.scrollLeft-s.x-u.x,y:l.top+i.scrollTop-s.y-u.y,width:l.width,height:l.height}}function mi(e){return"static"===Xl(e).position}function hi(e,t){if(!Vl(e)||"fixed"===Xl(e).position)return null;if(t)return t(e);let n=e.offsetParent;return Hl(e)===n&&(n=n.ownerDocument.body),n}function gi(e,t){const n=Fl(e);if(Kl(e))return n;if(!Vl(e)){let t=Jl(e);for(;t&&!Yl(t);){if(Wl(t)&&!mi(t))return t;t=Jl(t)}return n}let r=hi(e,t);for(;r&&ql(r)&&mi(r);)r=hi(r,t);return r&&Yl(r)&&mi(r)&&!Ql(r)?n:r||function(e){let t=Jl(e);for(;Vl(t)&&!Yl(t);){if(Ql(t))return t;if(Kl(t))return null;t=Jl(t)}return null}(e)||n}const vi={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e;const a="fixed"===o,l=Hl(r),i=!!t&&Kl(t.floating);if(r===l||i&&a)return n;let s={scrollLeft:0,scrollTop:0},c=ml(1);const u=ml(0),d=Vl(r);if((d||!d&&!a)&&(("body"!==Il(r)||$l(l))&&(s=Zl(r)),Vl(r))){const e=si(r);c=ai(r),u.x=e.x+r.clientLeft,u.y=e.y+r.clientTop}const f=!l||d||a?ml(0):ui(l,s,!0);return{width:n.width*c.x,height:n.height*c.y,x:n.x*c.x-s.scrollLeft*c.x+u.x+f.x,y:n.y*c.y-s.scrollTop*c.y+u.y+f.y}},getDocumentElement:Hl,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e;const a=[..."clippingAncestors"===n?Kl(t)?[]:function(e,t){const n=t.get(e);if(n)return n;let r=ti(e,[],!1).filter((e=>Wl(e)&&"body"!==Il(e))),o=null;const a="fixed"===Xl(e).position;let l=a?Jl(e):e;for(;Wl(l)&&!Yl(l);){const t=Xl(l),n=Ql(l);n||"fixed"!==t.position||(o=null),(a?!n&&!o:!n&&"static"===t.position&&o&&["absolute","fixed"].includes(o.position)||$l(l)&&!n&&fi(e,l))?r=r.filter((e=>e!==l)):o=t,l=Jl(l)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=a[0],i=a.reduce(((e,n)=>{const r=di(t,n,o);return e.top=dl(r.top,e.top),e.right=ul(r.right,e.right),e.bottom=ul(r.bottom,e.bottom),e.left=dl(r.left,e.left),e}),di(t,l,o));return{width:i.right-i.left,height:i.bottom-i.top,x:i.left,y:i.top}},getOffsetParent:gi,getElementRects:async function(e){const t=this.getOffsetParent||gi,n=this.getDimensions,r=await n(e.floating);return{reference:pi(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){const{width:t,height:n}=ri(e);return{width:t,height:n}},getScale:ai,isElement:Wl,isRTL:function(e){return"rtl"===Xl(e).direction}};function yi(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}function bi(e,t,n,r){void 0===r&&(r={});const{ancestorScroll:o=!0,ancestorResize:a=!0,elementResize:l="function"===typeof ResizeObserver,layoutShift:i="function"===typeof IntersectionObserver,animationFrame:s=!1}=r,c=oi(e),d=o||a?[...c?ti(c):[],...ti(t)]:[];d.forEach((e=>{o&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)}));const f=c&&i?function(e,t){let n,r=null;const o=Hl(e);function a(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return function l(i,s){void 0===i&&(i=!1),void 0===s&&(s=1),a();const c=e.getBoundingClientRect(),{left:d,top:f,width:p,height:m}=c;if(i||t(),!p||!m)return;const h={rootMargin:-pl(f)+"px "+-pl(o.clientWidth-(d+p))+"px "+-pl(o.clientHeight-(f+m))+"px "+-pl(d)+"px",threshold:dl(0,ul(1,s))||1};let g=!0;function v(t){const r=t[0].intersectionRatio;if(r!==s){if(!g)return l();r?l(!1,r):n=setTimeout((()=>{l(!1,1e-7)}),1e3)}1!==r||yi(c,e.getBoundingClientRect())||l(),g=!1}try{r=new IntersectionObserver(v,u(u({},h),{},{root:o.ownerDocument}))}catch(y){r=new IntersectionObserver(v,h)}r.observe(e)}(!0),a}(c,n):null;let p,m=-1,h=null;l&&(h=new ResizeObserver((e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(m),m=requestAnimationFrame((()=>{var e;null==(e=h)||e.observe(t)}))),n()})),c&&!s&&h.observe(c),h.observe(t));let g=s?si(e):null;return s&&function t(){const r=si(e);g&&!yi(g,r)&&n();g=r,p=requestAnimationFrame(t)}(),n(),()=>{var e;d.forEach((e=>{o&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)})),null==f||f(),null==(e=h)||e.disconnect(),h=null,s&&cancelAnimationFrame(p)}}const wi=function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;const{x:o,y:a,placement:l,middlewareData:i}=t,s=await async function(e,t){const{placement:n,platform:r,elements:o}=e,a=await(null==r.isRTL?void 0:r.isRTL(o.floating)),l=bl(n),i=wl(n),s="y"===Sl(n),c=["left","top"].includes(l)?-1:1,u=a&&s?-1:1,d=yl(t,e);let{mainAxis:f,crossAxis:p,alignmentAxis:m}="number"===typeof d?{mainAxis:d,crossAxis:0,alignmentAxis:null}:{mainAxis:d.mainAxis||0,crossAxis:d.crossAxis||0,alignmentAxis:d.alignmentAxis};return i&&"number"===typeof m&&(p="end"===i?-1*m:m),s?{x:p*u,y:f*c}:{x:f*c,y:p*u}}(t,e);return l===(null==(n=i.offset)?void 0:n.placement)&&null!=(r=i.arrow)&&r.alignmentOffset?{}:{x:o+s.x,y:a+s.y,data:u(u({},s),{},{placement:l})}}}},xi=function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){const{x:n,y:r,placement:o}=t,a=yl(e,t),{mainAxis:l=!0,crossAxis:i=!1,limiter:s={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}}}=a,c=d(a,Rl),f={x:n,y:r},p=await Ol(t,c),m=Sl(bl(o)),h=xl(m);let g=f[h],v=f[m];if(l){const e="y"===h?"bottom":"right";g=vl(g+p["y"===h?"top":"left"],g,g-p[e])}if(i){const e="y"===m?"bottom":"right";v=vl(v+p["y"===m?"top":"left"],v,v-p[e])}const y=s.fn(u(u({},t),{},{[h]:g,[m]:v}));return u(u({},y),{},{data:{x:y.x-n,y:y.y-r,enabled:{[h]:l,[m]:i}}})}}},ki=function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r;const{placement:o,middlewareData:a,rects:l,initialPlacement:i,platform:s,elements:c}=t,u=yl(e,t),{mainAxis:f=!0,crossAxis:p=!0,fallbackPlacements:m,fallbackStrategy:h="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:v=!0}=u,y=d(u,_l);if(null!=(n=a.arrow)&&n.alignmentOffset)return{};const b=bl(o),w=Sl(i),x=bl(i)===i,k=await(null==s.isRTL?void 0:s.isRTL(c.floating)),S=m||(x||!v?[Cl(i)]:function(e){const t=Cl(e);return[El(e),t,El(t)]}(i)),N="none"!==g;!m&&N&&S.push(...function(e,t,n,r){const o=wl(e);let a=function(e,t,n){const r=["left","right"],o=["right","left"],a=["top","bottom"],l=["bottom","top"];switch(e){case"top":case"bottom":return n?t?o:r:t?r:o;case"left":case"right":return t?a:l;default:return[]}}(bl(e),"start"===n,r);return o&&(a=a.map((e=>e+"-"+o)),t&&(a=a.concat(a.map(El)))),a}(i,v,g,k));const E=[i,...S],C=await Ol(t,y),j=[];let P=(null==(r=a.flip)?void 0:r.overflows)||[];if(f&&j.push(C[b]),p){const e=function(e,t,n){void 0===n&&(n=!1);const r=wl(e),o=Nl(e),a=kl(o);let l="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[a]>t.floating[a]&&(l=Cl(l)),[l,Cl(l)]}(o,l,k);j.push(C[e[0]],C[e[1]])}if(P=[...P,{placement:o,overflows:j}],!j.every((e=>e<=0))){var _,T;const e=((null==(_=a.flip)?void 0:_.index)||0)+1,t=E[e];if(t){var R;const n="alignment"===p&&w!==Sl(t),r=(null==(R=P[0])?void 0:R.overflows[0])>0;if(!n||r)return{data:{index:e,overflows:P},reset:{placement:t}}}let n=null==(T=P.filter((e=>e.overflows[0]<=0)).sort(((e,t)=>e.overflows[1]-t.overflows[1]))[0])?void 0:T.placement;if(!n)switch(h){case"bestFit":{var A;const e=null==(A=P.filter((e=>{if(N){const t=Sl(e.placement);return t===w||"y"===t}return!0})).map((e=>[e.placement,e.overflows.filter((e=>e>0)).reduce(((e,t)=>e+t),0)])).sort(((e,t)=>e[1]-t[1]))[0])?void 0:A[0];e&&(n=e);break}case"initialPlacement":n=i}if(o!==n)return{reset:{placement:n}}}return{}}}},Si=function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;const{placement:o,rects:a,platform:l,elements:i}=t,s=yl(e,t),{apply:c=()=>{}}=s,f=d(s,Al),p=await Ol(t,f),m=bl(o),h=wl(o),g="y"===Sl(o),{width:v,height:y}=a.floating;let b,w;"top"===m||"bottom"===m?(b=m,w=h===(await(null==l.isRTL?void 0:l.isRTL(i.floating))?"start":"end")?"left":"right"):(w=m,b="end"===h?"top":"bottom");const x=y-p.top-p.bottom,k=v-p.left-p.right,S=ul(y-p[b],x),N=ul(v-p[w],k),E=!t.middlewareData.shift;let C=S,j=N;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(j=k),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(C=x),E&&!h){const e=dl(p.left,0),t=dl(p.right,0),n=dl(p.top,0),r=dl(p.bottom,0);g?j=v-2*(0!==e||0!==t?e+t:dl(p.left,p.right)):C=y-2*(0!==n||0!==r?n+r:dl(p.top,p.bottom))}await c(u(u({},t),{},{availableWidth:j,availableHeight:C}));const P=await l.getDimensions(i.floating);return v!==P.width||y!==P.height?{reset:{rects:!0}}:{}}}},Ni=function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){const{rects:n}=t,r=yl(e,t),{strategy:o="referenceHidden"}=r,a=d(r,Tl);switch(o){case"referenceHidden":{const e=Ll(await Ol(t,u(u({},a),{},{elementContext:"reference"})),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:Ml(e)}}}case"escaped":{const e=Ll(await Ol(t,u(u({},a),{},{altBoundary:!0})),n.floating);return{data:{escapedOffsets:e,escaped:Ml(e)}}}default:return{}}}}},Ei=e=>({name:"arrow",options:e,async fn(t){const{x:n,y:r,placement:o,rects:a,platform:l,elements:i,middlewareData:s}=t,{element:c,padding:d=0}=yl(e,t)||{};if(null==c)return{};const f=jl(d),p={x:n,y:r},m=Nl(o),h=kl(m),g=await l.getDimensions(c),v="y"===m,y=v?"top":"left",b=v?"bottom":"right",w=v?"clientHeight":"clientWidth",x=a.reference[h]+a.reference[m]-p[m]-a.floating[h],k=p[m]-a.reference[m],S=await(null==l.getOffsetParent?void 0:l.getOffsetParent(c));let N=S?S[w]:0;N&&await(null==l.isElement?void 0:l.isElement(S))||(N=i.floating[w]||a.floating[h]);const E=x/2-k/2,C=N/2-g[h]/2-1,j=ul(f[y],C),P=ul(f[b],C),_=j,T=N-g[h]-P,R=N/2-g[h]/2+E,A=vl(_,R,T),z=!s.arrow&&null!=wl(o)&&R!==A&&a.reference[h]/2-(R<_?j:P)-g[h]/2<0,O=z?R<_?R-_:R-T:0;return{[m]:p[m]+O,data:u({[m]:A,centerOffset:R-A-O},z&&{alignmentOffset:O}),reset:z}}}),Ci=function(e){return void 0===e&&(e={}),{options:e,fn(t){const{x:n,y:r,placement:o,rects:a,middlewareData:l}=t,{offset:i=0,mainAxis:s=!0,crossAxis:c=!0}=yl(e,t),d={x:n,y:r},f=Sl(o),p=xl(f);let m=d[p],h=d[f];const g=yl(i,t),v="number"===typeof g?{mainAxis:g,crossAxis:0}:u({mainAxis:0,crossAxis:0},g);if(s){const e="y"===p?"height":"width",t=a.reference[p]-a.floating[e]+v.mainAxis,n=a.reference[p]+a.reference[e]-v.mainAxis;m<t?m=t:m>n&&(m=n)}if(c){var y,b;const e="y"===p?"width":"height",t=["top","left"].includes(bl(o)),n=a.reference[f]-a.floating[e]+(t&&(null==(y=l.offset)?void 0:y[f])||0)+(t?0:v.crossAxis),r=a.reference[f]+a.reference[e]+(t?0:(null==(b=l.offset)?void 0:b[f])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[p]:m,[f]:h}}}},ji=(e,t,n)=>{const r=new Map,o=u({platform:vi},n),a=u(u({},o.platform),{},{_c:r});return(async(e,t,n)=>{const{placement:r="bottom",strategy:o="absolute",middleware:a=[],platform:l}=n,i=a.filter(Boolean),s=await(null==l.isRTL?void 0:l.isRTL(t));let c=await l.getElementRects({reference:e,floating:t,strategy:o}),{x:d,y:f}=zl(c,r,s),p=r,m={},h=0;for(let g=0;g<i.length;g++){const{name:n,fn:a}=i[g],{x:v,y:y,data:b,reset:w}=await a({x:d,y:f,initialPlacement:r,placement:p,strategy:o,middlewareData:m,rects:c,platform:l,elements:{reference:e,floating:t}});d=null!=v?v:d,f=null!=y?y:f,m=u(u({},m),{},{[n]:u(u({},m[n]),b)}),w&&h<=50&&(h++,"object"===typeof w&&(w.placement&&(p=w.placement),w.rects&&(c=!0===w.rects?await l.getElementRects({reference:e,floating:t,strategy:o}):w.rects),({x:d,y:f}=zl(c,p,s))),g=-1)}return{x:d,y:f,placement:p,strategy:o,middlewareData:m}})(e,t,u(u({},o),{},{platform:a}))};var Pi="undefined"!==typeof document?r.useLayoutEffect:r.useEffect;function _i(e,t){if(e===t)return!0;if(typeof e!==typeof t)return!1;if("function"===typeof e&&e.toString()===t.toString())return!0;let n,r,o;if(e&&t&&"object"===typeof e){if(Array.isArray(e)){if(n=e.length,n!==t.length)return!1;for(r=n;0!==r--;)if(!_i(e[r],t[r]))return!1;return!0}if(o=Object.keys(e),n=o.length,n!==Object.keys(t).length)return!1;for(r=n;0!==r--;)if(!{}.hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!==r--;){const n=o[r];if(("_owner"!==n||!e.$$typeof)&&!_i(e[n],t[n]))return!1}return!0}return e!==e&&t!==t}function Ti(e){if("undefined"===typeof window)return 1;return(e.ownerDocument.defaultView||window).devicePixelRatio||1}function Ri(e,t){const n=Ti(e);return Math.round(t*n)/n}function Ai(e){const t=r.useRef(e);return Pi((()=>{t.current=e})),t}const zi=(e,t)=>u(u({},wi(e)),{},{options:[e,t]}),Oi=(e,t)=>u(u({},xi(e)),{},{options:[e,t]}),Li=(e,t)=>u(u({},Ci(e)),{},{options:[e,t]}),Mi=(e,t)=>u(u({},ki(e)),{},{options:[e,t]}),Di=(e,t)=>u(u({},Si(e)),{},{options:[e,t]}),Ii=(e,t)=>u(u({},Ni(e)),{},{options:[e,t]}),Fi=(e,t)=>u(u({},(e=>({name:"arrow",options:e,fn(t){const{element:n,padding:r}="function"===typeof e?e(t):e;return n&&(o=n,{}.hasOwnProperty.call(o,"current"))?null!=n.current?Ei({element:n.current,padding:r}).fn(t):{}:n?Ei({element:n,padding:r}).fn(t):{};var o}}))(e)),{},{options:[e,t]}),Hi=["children","width","height"];var Ui=r.forwardRef(((e,t)=>{const{children:n,width:r=10,height:o=5}=e,a=d(e,Hi);return(0,T.jsx)(G.svg,u(u({},a),{},{ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,T.jsx)("polygon",{points:"0,0 30,0 15,10"})}))}));Ui.displayName="Arrow";var Wi=Ui;const Vi=["__scopePopper","virtualRef"],Bi=["__scopePopper","side","sideOffset","align","alignOffset","arrowPadding","avoidCollisions","collisionBoundary","collisionPadding","sticky","hideWhenDetached","updatePositionStrategy","onPlaced"],$i=["__scopePopper"];var qi="Popper",[Ki,Qi]=z(qi),[Gi,Yi]=Ki(qi),Xi=e=>{const{__scopePopper:t,children:n}=e,[o,a]=r.useState(null);return(0,T.jsx)(Gi,{scope:t,anchor:o,onAnchorChange:a,children:n})};Xi.displayName=qi;var Zi="PopperAnchor",Ji=r.forwardRef(((e,t)=>{const{__scopePopper:n,virtualRef:o}=e,a=d(e,Vi),l=Yi(Zi,n),i=r.useRef(null),s=E(t,i);return r.useEffect((()=>{l.onAnchorChange((null===o||void 0===o?void 0:o.current)||i.current)})),o?null:(0,T.jsx)(G.div,u(u({},a),{},{ref:s}))}));Ji.displayName=Zi;var es="PopperContent",[ts,ns]=Ki(es),rs=r.forwardRef(((e,t)=>{var n,o,a,l,i,s,c,f;const{__scopePopper:p,side:m="bottom",sideOffset:h=0,align:g="center",alignOffset:v=0,arrowPadding:y=0,avoidCollisions:b=!0,collisionBoundary:w=[],collisionPadding:k=0,sticky:S="partial",hideWhenDetached:N=!1,updatePositionStrategy:C="optimized",onPlaced:j}=e,P=d(e,Bi),_=Yi(es,p),[R,A]=r.useState(null),z=E(t,(e=>A(e))),[O,L]=r.useState(null),M=function(e){const[t,n]=r.useState(void 0);return ue((()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});const t=new ResizeObserver((t=>{if(!Array.isArray(t))return;if(!t.length)return;const r=t[0];let o,a;if("borderBoxSize"in r){const e=r.borderBoxSize,t=Array.isArray(e)?e[0]:e;o=t.inlineSize,a=t.blockSize}else o=e.offsetWidth,a=e.offsetHeight;n({width:o,height:a})}));return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)}),[e]),t}(O),D=null!==(n=null===M||void 0===M?void 0:M.width)&&void 0!==n?n:0,I=null!==(o=null===M||void 0===M?void 0:M.height)&&void 0!==o?o:0,F=m+("center"!==g?"-"+g:""),H="number"===typeof k?k:u({top:0,right:0,bottom:0,left:0},k),U=Array.isArray(w)?w:[w],W=U.length>0,V={padding:H,boundary:U.filter(is),altBoundary:W},{refs:B,floatingStyles:$,placement:q,isPositioned:K,middlewareData:Q}=function(e){void 0===e&&(e={});const{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:a,elements:{reference:l,floating:i}={},transform:s=!0,whileElementsMounted:c,open:d}=e,[f,p]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[m,h]=r.useState(o);_i(m,o)||h(o);const[g,v]=r.useState(null),[y,b]=r.useState(null),w=r.useCallback((e=>{e!==E.current&&(E.current=e,v(e))}),[]),k=r.useCallback((e=>{e!==C.current&&(C.current=e,b(e))}),[]),S=l||g,N=i||y,E=r.useRef(null),C=r.useRef(null),j=r.useRef(f),P=null!=c,_=Ai(c),T=Ai(a),R=Ai(d),A=r.useCallback((()=>{if(!E.current||!C.current)return;const e={placement:t,strategy:n,middleware:m};T.current&&(e.platform=T.current),ji(E.current,C.current,e).then((e=>{const t=u(u({},e),{},{isPositioned:!1!==R.current});z.current&&!_i(j.current,t)&&(j.current=t,x.flushSync((()=>{p(t)})))}))}),[m,t,n,T,R]);Pi((()=>{!1===d&&j.current.isPositioned&&(j.current.isPositioned=!1,p((e=>u(u({},e),{},{isPositioned:!1}))))}),[d]);const z=r.useRef(!1);Pi((()=>(z.current=!0,()=>{z.current=!1})),[]),Pi((()=>{if(S&&(E.current=S),N&&(C.current=N),S&&N){if(_.current)return _.current(S,N,A);A()}}),[S,N,A,_,P]);const O=r.useMemo((()=>({reference:E,floating:C,setReference:w,setFloating:k})),[w,k]),L=r.useMemo((()=>({reference:S,floating:N})),[S,N]),M=r.useMemo((()=>{const e={position:n,left:0,top:0};if(!L.floating)return e;const t=Ri(L.floating,f.x),r=Ri(L.floating,f.y);return s?u(u({},e),{},{transform:"translate("+t+"px, "+r+"px)"},Ti(L.floating)>=1.5&&{willChange:"transform"}):{position:n,left:t,top:r}}),[n,s,L.floating,f.x,f.y]);return r.useMemo((()=>u(u({},f),{},{update:A,refs:O,elements:L,floatingStyles:M})),[f,A,O,L,M])}({strategy:"fixed",placement:F,whileElementsMounted:function(){for(var e=arguments.length,t=new Array(e),n=0;n<e;n++)t[n]=arguments[n];return bi(...t,{animationFrame:"always"===C})},elements:{reference:_.anchor},middleware:[zi({mainAxis:h+I,alignmentAxis:v}),b&&Oi(u({mainAxis:!0,crossAxis:!1,limiter:"partial"===S?Li():void 0},V)),b&&Mi(u({},V)),Di(u(u({},V),{},{apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e;const{width:a,height:l}=n.reference,i=t.floating.style;i.setProperty("--radix-popper-available-width","".concat(r,"px")),i.setProperty("--radix-popper-available-height","".concat(o,"px")),i.setProperty("--radix-popper-anchor-width","".concat(a,"px")),i.setProperty("--radix-popper-anchor-height","".concat(l,"px"))}})),O&&Fi({element:O,padding:y}),ss({arrowWidth:D,arrowHeight:I}),N&&Ii(u({strategy:"referenceHidden"},V))]}),[Y,Z]=cs(q),J=X(j);ue((()=>{K&&(null===J||void 0===J||J())}),[K,J]);const ee=null===(a=Q.arrow)||void 0===a?void 0:a.x,te=null===(l=Q.arrow)||void 0===l?void 0:l.y,ne=0!==(null===(i=Q.arrow)||void 0===i?void 0:i.centerOffset),[re,oe]=r.useState();return ue((()=>{R&&oe(window.getComputedStyle(R).zIndex)}),[R]),(0,T.jsx)("div",{ref:B.setFloating,"data-radix-popper-content-wrapper":"",style:u(u({},$),{},{transform:K?$.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:re,"--radix-popper-transform-origin":[null===(s=Q.transformOrigin)||void 0===s?void 0:s.x,null===(c=Q.transformOrigin)||void 0===c?void 0:c.y].join(" ")},(null===(f=Q.hide)||void 0===f?void 0:f.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}),dir:e.dir,children:(0,T.jsx)(ts,{scope:p,placedSide:Y,onArrowChange:L,arrowX:ee,arrowY:te,shouldHideArrow:ne,children:(0,T.jsx)(G.div,u(u({"data-side":Y,"data-align":Z},P),{},{ref:z,style:u(u({},P.style),{},{animation:K?void 0:"none"})}))})})}));rs.displayName=es;var os="PopperArrow",as={top:"bottom",right:"left",bottom:"top",left:"right"},ls=r.forwardRef((function(e,t){const{__scopePopper:n}=e,r=d(e,$i),o=ns(os,n),a=as[o.placedSide];return(0,T.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[a]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,T.jsx)(Wi,u(u({},r),{},{ref:t,style:u(u({},r.style),{},{display:"block"})}))})}));function is(e){return null!==e}ls.displayName=os;var ss=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,a,l;const{placement:i,rects:s,middlewareData:c}=t,u=0!==(null===(n=c.arrow)||void 0===n?void 0:n.centerOffset),d=u?0:e.arrowWidth,f=u?0:e.arrowHeight,[p,m]=cs(i),h={start:"0%",center:"50%",end:"100%"}[m],g=(null!==(r=null===(o=c.arrow)||void 0===o?void 0:o.x)&&void 0!==r?r:0)+d/2,v=(null!==(a=null===(l=c.arrow)||void 0===l?void 0:l.y)&&void 0!==a?a:0)+f/2;let y="",b="";return"bottom"===p?(y=u?h:"".concat(g,"px"),b="".concat(-f,"px")):"top"===p?(y=u?h:"".concat(g,"px"),b="".concat(s.floating.height+f,"px")):"right"===p?(y="".concat(-f,"px"),b=u?h:"".concat(v,"px")):"left"===p&&(y="".concat(s.floating.width+f,"px"),b=u?h:"".concat(v,"px")),{data:{x:y,y:b}}}});function cs(e){const[t,n="center"]=e.split("-");return[t,n]}var us=Xi,ds=Ji,fs=rs,ps=ls;const ms=["__scopeSelect","disabled"],hs=["__scopeSelect","className","style","children","placeholder"],gs=["__scopeSelect","children"],vs=["__scopeSelect","position","onCloseAutoFocus","onEscapeKeyDown","onPointerDownOutside","side","sideOffset","align","alignOffset","arrowPadding","collisionBoundary","collisionPadding","sticky","hideWhenDetached","avoidCollisions"],ys=["__scopeSelect","onPlaced"],bs=["__scopeSelect","align","collisionPadding"],ws=["__scopeSelect","nonce"],xs=["__scopeSelect"],ks=["__scopeSelect"],Ss=["__scopeSelect","value","disabled","textValue"],Ns=["__scopeSelect","className","style"],Es=["__scopeSelect"],Cs=["__scopeSelect","onAutoScroll"],js=["__scopeSelect"],Ps=["__scopeSelect"],_s=["__scopeSelect","value"];var Ts=[" ","Enter","ArrowUp","ArrowDown"],Rs=[" ","Enter"],As="Select",[zs,Os,Ls]=B(As),[Ms,Ds]=z(As,[Ls,Qi]),Is=Qi(),[Fs,Hs]=Ms(As),[Us,Ws]=Ms(As),Vs=e=>{const{__scopeSelect:t,children:n,open:o,defaultOpen:a,onOpenChange:l,value:i,defaultValue:s,onValueChange:c,dir:d,name:f,autoComplete:p,disabled:m,required:h,form:g}=e,v=Is(t),[y,b]=r.useState(null),[w,x]=r.useState(null),[k,S]=r.useState(!1),N=function(e){const t=r.useContext(sl);return e||t||"ltr"}(d),[E,C]=ge({prop:o,defaultProp:null!==a&&void 0!==a&&a,onChange:l,caller:As}),[j,P]=ge({prop:i,defaultProp:s,onChange:c,caller:As}),_=r.useRef(null),R=!y||(g||!!y.closest("form")),[A,z]=r.useState(new Set),O=Array.from(A).map((e=>e.props.value)).join(";");return(0,T.jsx)(us,u(u({},v),{},{children:(0,T.jsxs)(Fs,{required:h,scope:t,trigger:y,onTriggerChange:b,valueNode:w,onValueNodeChange:x,valueNodeHasChildren:k,onValueNodeHasChildrenChange:S,contentId:Mr(),value:j,onValueChange:P,open:E,onOpenChange:C,dir:N,triggerPointerDownPosRef:_,disabled:m,children:[(0,T.jsx)(zs.Provider,{scope:t,children:(0,T.jsx)(Us,{scope:e.__scopeSelect,onNativeOptionAdd:r.useCallback((e=>{z((t=>new Set(t).add(e)))}),[]),onNativeOptionRemove:r.useCallback((e=>{z((t=>{const n=new Set(t);return n.delete(e),n}))}),[]),children:n})}),R?(0,T.jsxs)(Rc,{"aria-hidden":!0,required:h,tabIndex:-1,name:f,autoComplete:p,value:j,onChange:e=>P(e.target.value),disabled:m,form:g,children:[void 0===j?(0,T.jsx)("option",{value:""}):null,Array.from(A)]},O):null]})}))};Vs.displayName=As;var Bs="SelectTrigger",$s=r.forwardRef(((e,t)=>{const{__scopeSelect:n,disabled:o=!1}=e,a=d(e,ms),l=Is(n),i=Hs(Bs,n),s=i.disabled||o,c=E(t,i.onTriggerChange),f=Os(n),p=r.useRef("touch"),[m,h,g]=zc((e=>{const t=f().filter((e=>!e.disabled)),n=t.find((e=>e.value===i.value)),r=Oc(t,e,n);void 0!==r&&i.onValueChange(r.value)})),v=e=>{s||(i.onOpenChange(!0),g()),e&&(i.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,T.jsx)(ds,u(u({asChild:!0},l),{},{children:(0,T.jsx)(G.button,u(u({type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:s,"data-disabled":s?"":void 0,"data-placeholder":Ac(i.value)?"":void 0},a),{},{ref:c,onClick:k(a.onClick,(e=>{e.currentTarget.focus(),"mouse"!==p.current&&v(e)})),onPointerDown:k(a.onPointerDown,(e=>{p.current=e.pointerType;const t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(v(e),e.preventDefault())})),onKeyDown:k(a.onKeyDown,(e=>{const t=""!==m.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||h(e.key),t&&" "===e.key||Ts.includes(e.key)&&(v(),e.preventDefault())}))}))}))}));$s.displayName=Bs;var qs="SelectValue",Ks=r.forwardRef(((e,t)=>{const{__scopeSelect:n,className:r,style:o,children:a,placeholder:l=""}=e,i=d(e,hs),s=Hs(qs,n),{onValueNodeHasChildrenChange:c}=s,f=void 0!==a,p=E(t,s.onValueNodeChange);return ue((()=>{c(f)}),[c,f]),(0,T.jsx)(G.span,u(u({},i),{},{ref:p,style:{pointerEvents:"none"},children:Ac(s.value)?(0,T.jsx)(T.Fragment,{children:l}):a}))}));Ks.displayName=qs;var Qs=r.forwardRef(((e,t)=>{const{__scopeSelect:n,children:r}=e,o=d(e,gs);return(0,T.jsx)(G.span,u(u({"aria-hidden":!0},o),{},{ref:t,children:r||"\u25bc"}))}));Qs.displayName="SelectIcon";var Gs=e=>(0,T.jsx)(fe,u({asChild:!0},e));Gs.displayName="SelectPortal";var Ys="SelectContent",Xs=r.forwardRef(((e,t)=>{const n=Hs(Ys,e.__scopeSelect),[o,a]=r.useState();if(ue((()=>{a(new DocumentFragment)}),[]),!n.open){const t=o;return t?x.createPortal((0,T.jsx)(Js,{scope:e.__scopeSelect,children:(0,T.jsx)(zs.Slot,{scope:e.__scopeSelect,children:(0,T.jsx)("div",{children:e.children})})}),t):null}return(0,T.jsx)(nc,u(u({},e),{},{ref:t}))}));Xs.displayName=Ys;var Zs=10,[Js,ec]=Ms(Ys),tc=D("SelectContent.RemoveScroll"),nc=r.forwardRef(((e,t)=>{const{__scopeSelect:n,position:o="item-aligned",onCloseAutoFocus:a,onEscapeKeyDown:l,onPointerDownOutside:i,side:s,sideOffset:c,align:f,alignOffset:p,arrowPadding:m,collisionBoundary:h,collisionPadding:g,sticky:v,hideWhenDetached:y,avoidCollisions:b}=e,w=d(e,vs),x=Hs(Ys,n),[S,N]=r.useState(null),[C,j]=r.useState(null),P=E(t,(e=>N(e))),[_,R]=r.useState(null),[A,z]=r.useState(null),O=Os(n),[L,M]=r.useState(!1),D=r.useRef(!1);r.useEffect((()=>{if(S)return Yo(S)}),[S]),Gr();const I=r.useCallback((e=>{const[t,...n]=O().map((e=>e.ref.current)),[r]=n.slice(-1),o=document.activeElement;for(const a of e){if(a===o)return;if(null===a||void 0===a||a.scrollIntoView({block:"nearest"}),a===t&&C&&(C.scrollTop=0),a===r&&C&&(C.scrollTop=C.scrollHeight),null===a||void 0===a||a.focus(),document.activeElement!==o)return}}),[O,C]),F=r.useCallback((()=>I([_,S])),[I,_,S]);r.useEffect((()=>{L&&F()}),[L,F]);const{onOpenChange:H,triggerPointerDownPosRef:U}=x;r.useEffect((()=>{if(S){let e={x:0,y:0};const t=t=>{var n,r,o,a;e={x:Math.abs(Math.round(t.pageX)-(null!==(n=null===(r=U.current)||void 0===r?void 0:r.x)&&void 0!==n?n:0)),y:Math.abs(Math.round(t.pageY)-(null!==(o=null===(a=U.current)||void 0===a?void 0:a.y)&&void 0!==o?o:0))}},n=n=>{e.x<=10&&e.y<=10?n.preventDefault():S.contains(n.target)||H(!1),document.removeEventListener("pointermove",t),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",n,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",n,{capture:!0})}}}),[S,H,U]),r.useEffect((()=>{const e=()=>H(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}}),[H]);const[W,V]=zc((e=>{const t=O().filter((e=>!e.disabled)),n=t.find((e=>e.ref.current===document.activeElement)),r=Oc(t,e,n);r&&setTimeout((()=>r.ref.current.focus()))})),B=r.useCallback(((e,t,n)=>{const r=!D.current&&!n;(void 0!==x.value&&x.value===t||r)&&(R(e),r&&(D.current=!0))}),[x.value]),$=r.useCallback((()=>null===S||void 0===S?void 0:S.focus()),[S]),q=r.useCallback(((e,t,n)=>{const r=!D.current&&!n;(void 0!==x.value&&x.value===t||r)&&z(e)}),[x.value]),K="popper"===o?oc:rc,Q=K===oc?{side:s,sideOffset:c,align:f,alignOffset:p,arrowPadding:m,collisionBoundary:h,collisionPadding:g,sticky:v,hideWhenDetached:y,avoidCollisions:b}:{};return(0,T.jsx)(Js,{scope:n,content:S,viewport:C,onViewportChange:j,itemRefCallback:B,selectedItem:_,onItemLeave:$,itemTextRefCallback:q,focusSelectedItem:F,selectedItemText:A,position:o,isPositioned:L,searchRef:W,children:(0,T.jsx)(Wo,{as:tc,allowPinchZoom:!0,children:(0,T.jsx)(Ur,{asChild:!0,trapped:x.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:k(a,(e=>{var t;null===(t=x.trigger)||void 0===t||t.focus({preventScroll:!0}),e.preventDefault()})),children:(0,T.jsx)(oe,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:i,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>x.onOpenChange(!1),children:(0,T.jsx)(K,u(u(u({role:"listbox",id:x.contentId,"data-state":x.open?"open":"closed",dir:x.dir,onContextMenu:e=>e.preventDefault()},w),Q),{},{onPlaced:()=>M(!0),ref:P,style:u({display:"flex",flexDirection:"column",outline:"none"},w.style),onKeyDown:k(w.onKeyDown,(e=>{const t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||V(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=O().filter((e=>!e.disabled)).map((e=>e.ref.current));if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){const n=e.target,r=t.indexOf(n);t=t.slice(r+1)}setTimeout((()=>I(t))),e.preventDefault()}}))}))})})})})}));nc.displayName="SelectContentImpl";var rc=r.forwardRef(((e,t)=>{const{__scopeSelect:n,onPlaced:o}=e,a=d(e,ys),l=Hs(Ys,n),i=ec(Ys,n),[s,c]=r.useState(null),[f,p]=r.useState(null),m=E(t,(e=>p(e))),h=Os(n),g=r.useRef(!1),v=r.useRef(!0),{viewport:y,selectedItem:b,selectedItemText:w,focusSelectedItem:x}=i,k=r.useCallback((()=>{if(l.trigger&&l.valueNode&&s&&f&&y&&b&&w){const e=l.trigger.getBoundingClientRect(),t=f.getBoundingClientRect(),n=l.valueNode.getBoundingClientRect(),r=w.getBoundingClientRect();if("rtl"!==l.dir){const o=r.left-t.left,a=n.left-o,l=e.left-a,i=e.width+l,c=Math.max(i,t.width),u=window.innerWidth-Zs,d=il(a,[Zs,Math.max(Zs,u-c)]);s.style.minWidth=i+"px",s.style.left=d+"px"}else{const o=t.right-r.right,a=window.innerWidth-n.right-o,l=window.innerWidth-e.right-a,i=e.width+l,c=Math.max(i,t.width),u=window.innerWidth-Zs,d=il(a,[Zs,Math.max(Zs,u-c)]);s.style.minWidth=i+"px",s.style.right=d+"px"}const a=h(),i=window.innerHeight-2*Zs,c=y.scrollHeight,u=window.getComputedStyle(f),d=parseInt(u.borderTopWidth,10),p=parseInt(u.paddingTop,10),m=parseInt(u.borderBottomWidth,10),v=d+p+c+parseInt(u.paddingBottom,10)+m,x=Math.min(5*b.offsetHeight,v),k=window.getComputedStyle(y),S=parseInt(k.paddingTop,10),N=parseInt(k.paddingBottom,10),E=e.top+e.height/2-Zs,C=i-E,j=b.offsetHeight/2,P=d+p+(b.offsetTop+j),_=v-P;if(P<=E){const e=a.length>0&&b===a[a.length-1].ref.current;s.style.bottom="0px";const t=f.clientHeight-y.offsetTop-y.offsetHeight,n=P+Math.max(C,j+(e?N:0)+t+m);s.style.height=n+"px"}else{const e=a.length>0&&b===a[0].ref.current;s.style.top="0px";const t=Math.max(E,d+y.offsetTop+(e?S:0)+j)+_;s.style.height=t+"px",y.scrollTop=P-E+y.offsetTop}s.style.margin="".concat(Zs,"px 0"),s.style.minHeight=x+"px",s.style.maxHeight=i+"px",null===o||void 0===o||o(),requestAnimationFrame((()=>g.current=!0))}}),[h,l.trigger,l.valueNode,s,f,y,b,w,l.dir,o]);ue((()=>k()),[k]);const[S,N]=r.useState();ue((()=>{f&&N(window.getComputedStyle(f).zIndex)}),[f]);const C=r.useCallback((e=>{e&&!0===v.current&&(k(),null===x||void 0===x||x(),v.current=!1)}),[k,x]);return(0,T.jsx)(ac,{scope:n,contentWrapper:s,shouldExpandOnScrollRef:g,onScrollButtonChange:C,children:(0,T.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:S},children:(0,T.jsx)(G.div,u(u({},a),{},{ref:m,style:u({boxSizing:"border-box",maxHeight:"100%"},a.style)}))})})}));rc.displayName="SelectItemAlignedPosition";var oc=r.forwardRef(((e,t)=>{const{__scopeSelect:n,align:r="start",collisionPadding:o=Zs}=e,a=d(e,bs),l=Is(n);return(0,T.jsx)(fs,u(u(u({},l),a),{},{ref:t,align:r,collisionPadding:o,style:u(u({boxSizing:"border-box"},a.style),{"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"})}))}));oc.displayName="SelectPopperPosition";var[ac,lc]=Ms(Ys,{}),ic="SelectViewport",sc=r.forwardRef(((e,t)=>{const{__scopeSelect:n,nonce:o}=e,a=d(e,ws),l=ec(ic,n),i=lc(ic,n),s=E(t,l.onViewportChange),c=r.useRef(0);return(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:o}),(0,T.jsx)(zs.Slot,{scope:n,children:(0,T.jsx)(G.div,u(u({"data-radix-select-viewport":"",role:"presentation"},a),{},{ref:s,style:u({position:"relative",flex:1,overflow:"hidden auto"},a.style),onScroll:k(a.onScroll,(e=>{const t=e.currentTarget,{contentWrapper:n,shouldExpandOnScrollRef:r}=i;if(null!==r&&void 0!==r&&r.current&&n){const e=Math.abs(c.current-t.scrollTop);if(e>0){const r=window.innerHeight-2*Zs,o=parseFloat(n.style.minHeight),a=parseFloat(n.style.height),l=Math.max(o,a);if(l<r){const o=l+e,a=Math.min(r,o),i=o-a;n.style.height=a+"px","0px"===n.style.bottom&&(t.scrollTop=i>0?i:0,n.style.justifyContent="flex-end")}}}c.current=t.scrollTop}))}))})]})}));sc.displayName=ic;var cc="SelectGroup",[uc,dc]=Ms(cc),fc=r.forwardRef(((e,t)=>{const{__scopeSelect:n}=e,r=d(e,xs),o=Mr();return(0,T.jsx)(uc,{scope:n,id:o,children:(0,T.jsx)(G.div,u(u({role:"group","aria-labelledby":o},r),{},{ref:t}))})}));fc.displayName=cc;var pc="SelectLabel",mc=r.forwardRef(((e,t)=>{const{__scopeSelect:n}=e,r=d(e,ks),o=dc(pc,n);return(0,T.jsx)(G.div,u(u({id:o.id},r),{},{ref:t}))}));mc.displayName=pc;var hc="SelectItem",[gc,vc]=Ms(hc),yc=r.forwardRef(((e,t)=>{const{__scopeSelect:n,value:o,disabled:a=!1,textValue:l}=e,i=d(e,Ss),s=Hs(hc,n),c=ec(hc,n),f=s.value===o,[p,m]=r.useState(null!==l&&void 0!==l?l:""),[h,g]=r.useState(!1),v=E(t,(e=>{var t;return null===(t=c.itemRefCallback)||void 0===t?void 0:t.call(c,e,o,a)})),y=Mr(),b=r.useRef("touch"),w=()=>{a||(s.onValueChange(o),s.onOpenChange(!1))};if(""===o)throw new Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,T.jsx)(gc,{scope:n,value:o,disabled:a,textId:y,isSelected:f,onItemTextChange:r.useCallback((e=>{m((t=>{var n;return t||(null!==(n=null===e||void 0===e?void 0:e.textContent)&&void 0!==n?n:"").trim()}))}),[]),children:(0,T.jsx)(zs.ItemSlot,{scope:n,value:o,disabled:a,textValue:p,children:(0,T.jsx)(G.div,u(u({role:"option","aria-labelledby":y,"data-highlighted":h?"":void 0,"aria-selected":f&&h,"data-state":f?"checked":"unchecked","aria-disabled":a||void 0,"data-disabled":a?"":void 0,tabIndex:a?void 0:-1},i),{},{ref:v,onFocus:k(i.onFocus,(()=>g(!0))),onBlur:k(i.onBlur,(()=>g(!1))),onClick:k(i.onClick,(()=>{"mouse"!==b.current&&w()})),onPointerUp:k(i.onPointerUp,(()=>{"mouse"===b.current&&w()})),onPointerDown:k(i.onPointerDown,(e=>{b.current=e.pointerType})),onPointerMove:k(i.onPointerMove,(e=>{var t;(b.current=e.pointerType,a)?null===(t=c.onItemLeave)||void 0===t||t.call(c):"mouse"===b.current&&e.currentTarget.focus({preventScroll:!0})})),onPointerLeave:k(i.onPointerLeave,(e=>{var t;e.currentTarget===document.activeElement&&(null===(t=c.onItemLeave)||void 0===t||t.call(c))})),onKeyDown:k(i.onKeyDown,(e=>{var t;""!==(null===(t=c.searchRef)||void 0===t?void 0:t.current)&&" "===e.key||(Rs.includes(e.key)&&w()," "===e.key&&e.preventDefault())}))}))})})}));yc.displayName=hc;var bc="SelectItemText",wc=r.forwardRef(((e,t)=>{const{__scopeSelect:n,className:o,style:a}=e,l=d(e,Ns),i=Hs(bc,n),s=ec(bc,n),c=vc(bc,n),f=Ws(bc,n),[p,m]=r.useState(null),h=E(t,(e=>m(e)),c.onItemTextChange,(e=>{var t;return null===(t=s.itemTextRefCallback)||void 0===t?void 0:t.call(s,e,c.value,c.disabled)})),g=null===p||void 0===p?void 0:p.textContent,v=r.useMemo((()=>(0,T.jsx)("option",{value:c.value,disabled:c.disabled,children:g},c.value)),[c.disabled,c.value,g]),{onNativeOptionAdd:y,onNativeOptionRemove:b}=f;return ue((()=>(y(v),()=>b(v))),[y,b,v]),(0,T.jsxs)(T.Fragment,{children:[(0,T.jsx)(G.span,u(u({id:c.textId},l),{},{ref:h})),c.isSelected&&i.valueNode&&!i.valueNodeHasChildren?x.createPortal(l.children,i.valueNode):null]})}));wc.displayName=bc;var xc="SelectItemIndicator",kc=r.forwardRef(((e,t)=>{const{__scopeSelect:n}=e,r=d(e,Es);return vc(xc,n).isSelected?(0,T.jsx)(G.span,u(u({"aria-hidden":!0},r),{},{ref:t})):null}));kc.displayName=xc;var Sc="SelectScrollUpButton",Nc=r.forwardRef(((e,t)=>{const n=ec(Sc,e.__scopeSelect),o=lc(Sc,e.__scopeSelect),[a,l]=r.useState(!1),i=E(t,o.onScrollButtonChange);return ue((()=>{if(n.viewport&&n.isPositioned){let e=function(){const e=t.scrollTop>0;l(e)};const t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[n.viewport,n.isPositioned]),a?(0,T.jsx)(jc,u(u({},e),{},{ref:i,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}})):null}));Nc.displayName=Sc;var Ec="SelectScrollDownButton",Cc=r.forwardRef(((e,t)=>{const n=ec(Ec,e.__scopeSelect),o=lc(Ec,e.__scopeSelect),[a,l]=r.useState(!1),i=E(t,o.onScrollButtonChange);return ue((()=>{if(n.viewport&&n.isPositioned){let e=function(){const e=t.scrollHeight-t.clientHeight,n=Math.ceil(t.scrollTop)<e;l(n)};const t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}}),[n.viewport,n.isPositioned]),a?(0,T.jsx)(jc,u(u({},e),{},{ref:i,onAutoScroll:()=>{const{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}})):null}));Cc.displayName=Ec;var jc=r.forwardRef(((e,t)=>{const{__scopeSelect:n,onAutoScroll:o}=e,a=d(e,Cs),l=ec("SelectScrollButton",n),i=r.useRef(null),s=Os(n),c=r.useCallback((()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)}),[]);return r.useEffect((()=>()=>c()),[c]),ue((()=>{var e;const t=s().find((e=>e.ref.current===document.activeElement));null===t||void 0===t||null===(e=t.ref.current)||void 0===e||e.scrollIntoView({block:"nearest"})}),[s]),(0,T.jsx)(G.div,u(u({"aria-hidden":!0},a),{},{ref:t,style:u({flexShrink:0},a.style),onPointerDown:k(a.onPointerDown,(()=>{null===i.current&&(i.current=window.setInterval(o,50))})),onPointerMove:k(a.onPointerMove,(()=>{var e;null===(e=l.onItemLeave)||void 0===e||e.call(l),null===i.current&&(i.current=window.setInterval(o,50))})),onPointerLeave:k(a.onPointerLeave,(()=>{c()}))}))})),Pc=r.forwardRef(((e,t)=>{const{__scopeSelect:n}=e,r=d(e,js);return(0,T.jsx)(G.div,u(u({"aria-hidden":!0},r),{},{ref:t}))}));Pc.displayName="SelectSeparator";var _c="SelectArrow",Tc=r.forwardRef(((e,t)=>{const{__scopeSelect:n}=e,r=d(e,Ps),o=Is(n),a=Hs(_c,n),l=ec(_c,n);return a.open&&"popper"===l.position?(0,T.jsx)(ps,u(u(u({},o),r),{},{ref:t})):null}));Tc.displayName=_c;var Rc=r.forwardRef(((e,t)=>{let{__scopeSelect:n,value:o}=e,a=d(e,_s);const l=r.useRef(null),i=E(t,l),s=function(e){const t=r.useRef({value:e,previous:e});return r.useMemo((()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous)),[e])}(o);return r.useEffect((()=>{const e=l.current;if(!e)return;const t=window.HTMLSelectElement.prototype,n=Object.getOwnPropertyDescriptor(t,"value").set;if(s!==o&&n){const t=new Event("change",{bubbles:!0});n.call(e,o),e.dispatchEvent(t)}}),[s,o]),(0,T.jsx)(G.select,u(u({},a),{},{style:u(u({},ve),a.style),ref:i,defaultValue:o}))}));function Ac(e){return""===e||void 0===e}function zc(e){const t=X(e),n=r.useRef(""),o=r.useRef(0),a=r.useCallback((e=>{const r=n.current+e;t(r),function e(t){n.current=t,window.clearTimeout(o.current),""!==t&&(o.current=window.setTimeout((()=>e("")),1e3))}(r)}),[t]),l=r.useCallback((()=>{n.current="",window.clearTimeout(o.current)}),[]);return r.useEffect((()=>()=>window.clearTimeout(o.current)),[]),[n,a,l]}function Oc(e,t,n){const r=t.length>1&&Array.from(t).every((e=>e===t[0]))?t[0]:t,o=n?e.indexOf(n):-1;let a=(l=e,i=Math.max(o,0),l.map(((e,t)=>l[(i+t)%l.length])));var l,i;1===r.length&&(a=a.filter((e=>e!==n)));const s=a.find((e=>e.textValue.toLowerCase().startsWith(r.toLowerCase())));return s!==n?s:void 0}Rc.displayName="SelectBubbleInput";var Lc=Vs,Mc=$s,Dc=Ks,Ic=Qs,Fc=Gs,Hc=Xs,Uc=sc,Wc=mc,Vc=yc,Bc=wc,$c=kc,qc=Nc,Kc=Cc,Qc=Pc;const Gc=Pt("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Yc=Pt("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),Xc=["className","children"],Zc=["className"],Jc=["className"],eu=["className","children","position"],tu=["className"],nu=["className","children"],ru=["className"],ou=Lc,au=Dc,lu=r.forwardRef(((e,t)=>{let{className:n,children:r}=e,o=d(e,Xc);return(0,T.jsxs)(Mc,u(u({ref:t,className:In("flex h-9 w-full items-center justify-between whitespace-nowrap rounded-md border border-input bg-transparent px-3 py-2 text-sm shadow-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none focus:ring-1 focus:ring-ring disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",n)},o),{},{children:[r,(0,T.jsx)(Ic,{asChild:!0,children:(0,T.jsx)(cr,{className:"h-4 w-4 opacity-50"})})]}))}));lu.displayName=Mc.displayName;const iu=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,Zc);return(0,T.jsx)(qc,u(u({ref:t,className:In("flex cursor-default items-center justify-center py-1",n)},r),{},{children:(0,T.jsx)(Gc,{className:"h-4 w-4"})}))}));iu.displayName=qc.displayName;const su=r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,Jc);return(0,T.jsx)(Kc,u(u({ref:t,className:In("flex cursor-default items-center justify-center py-1",n)},r),{},{children:(0,T.jsx)(cr,{className:"h-4 w-4"})}))}));su.displayName=Kc.displayName;const cu=r.forwardRef(((e,t)=>{let{className:n,children:r,position:o="popper"}=e,a=d(e,eu);return(0,T.jsx)(Fc,{children:(0,T.jsxs)(Hc,u(u({ref:t,className:In("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===o&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",n),position:o},a),{},{children:[(0,T.jsx)(iu,{}),(0,T.jsx)(Uc,{className:In("p-1","popper"===o&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:r}),(0,T.jsx)(su,{})]}))})}));cu.displayName=Hc.displayName;r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,tu);return(0,T.jsx)(Wc,u({ref:t,className:In("px-2 py-1.5 text-sm font-semibold",n)},r))})).displayName=Wc.displayName;const uu=r.forwardRef(((e,t)=>{let{className:n,children:r}=e,o=d(e,nu);return(0,T.jsxs)(Vc,u(u({ref:t,className:In("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-2 pr-8 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",n)},o),{},{children:[(0,T.jsx)("span",{className:"absolute right-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,T.jsx)($c,{children:(0,T.jsx)(Yc,{className:"h-4 w-4"})})}),(0,T.jsx)(Bc,{children:r})]}))}));uu.displayName=Vc.displayName;r.forwardRef(((e,t)=>{let{className:n}=e,r=d(e,ru);return(0,T.jsx)(Qc,u({ref:t,className:In("-mx-1 my-1 h-px bg-muted",n)},r))})).displayName=Qc.displayName;const du=Pt("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),fu=Pt("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]]),pu=Pt("phone",[["path",{d:"M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z",key:"foiqr5"}]]),mu=Pt("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]]),hu=Pt("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),gu=()=>{const{toast:e}=w(),[t,n]=(0,r.useState)({name:"",email:"",company:"",projectType:"",budget:"",message:"",timeline:""}),[o,a]=(0,r.useState)(!1),l=(e,t)=>{n((n=>u(u({},n),{},{[e]:t})))};return(0,T.jsx)("section",{id:"contact",className:"py-20 bg-gradient-to-b from-gray-900 to-black",children:(0,T.jsxs)("div",{className:"container mx-auto px-6",children:[(0,T.jsxs)("div",{className:"text-center mb-16",children:[(0,T.jsxs)("h2",{className:"text-5xl md:text-6xl font-bold mb-6",children:[(0,T.jsx)("span",{className:"bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent",children:"Let's Build"}),(0,T.jsx)("span",{className:"block bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent",children:"Together"})]}),(0,T.jsx)("p",{className:"text-xl text-gray-300 max-w-3xl mx-auto",children:"Ready to transform your vision into reality? Let's discuss how our AI expertise can elevate your business to new heights."})]}),(0,T.jsxs)("div",{className:"grid lg:grid-cols-3 gap-12",children:[(0,T.jsx)("div",{className:"lg:col-span-1",children:(0,T.jsxs)(vr,{className:"bg-gray-900/50 backdrop-blur-sm border-gray-800/50 h-fit",children:[(0,T.jsx)(yr,{children:(0,T.jsx)(br,{className:"text-2xl font-bold text-white mb-6",children:"Get In Touch"})}),(0,T.jsxs)(wr,{className:"space-y-6",children:[(0,T.jsxs)("div",{className:"space-y-4",children:[(0,T.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,T.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-400/20 to-yellow-600/20 rounded-lg flex items-center justify-center",children:(0,T.jsx)(fu,{className:"w-6 h-6 text-yellow-400"})}),(0,T.jsxs)("div",{children:[(0,T.jsx)("div",{className:"text-sm text-gray-400",children:"Email"}),(0,T.jsx)("div",{className:"text-white font-medium",children:"<EMAIL>"})]})]}),(0,T.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,T.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-400/20 to-yellow-600/20 rounded-lg flex items-center justify-center",children:(0,T.jsx)(pu,{className:"w-6 h-6 text-yellow-400"})}),(0,T.jsxs)("div",{children:[(0,T.jsx)("div",{className:"text-sm text-gray-400",children:"Phone"}),(0,T.jsx)("div",{className:"text-white font-medium",children:"+****************"})]})]}),(0,T.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,T.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-yellow-400/20 to-yellow-600/20 rounded-lg flex items-center justify-center",children:(0,T.jsx)(mu,{className:"w-6 h-6 text-yellow-400"})}),(0,T.jsxs)("div",{children:[(0,T.jsx)("div",{className:"text-sm text-gray-400",children:"Location"}),(0,T.jsx)("div",{className:"text-white font-medium",children:"San Francisco, CA"})]})]})]}),(0,T.jsx)("div",{className:"pt-6 border-t border-gray-800",children:(0,T.jsxs)("div",{className:"bg-yellow-400/10 border border-yellow-400/20 rounded-lg p-4",children:[(0,T.jsxs)("div",{className:"flex items-center space-x-2 mb-2",children:[(0,T.jsx)(du,{className:"w-5 h-5 text-yellow-400"}),(0,T.jsx)("span",{className:"text-yellow-400 font-medium",children:"Quick Response"})]}),(0,T.jsx)("p",{className:"text-gray-300 text-sm",children:"We typically respond within 4-6 hours during business hours."})]})})]})]})}),(0,T.jsx)("div",{className:"lg:col-span-2",children:(0,T.jsxs)(vr,{className:"bg-gray-900/50 backdrop-blur-sm border-gray-800/50",children:[(0,T.jsxs)(yr,{children:[(0,T.jsx)(br,{className:"text-2xl font-bold text-white",children:"Start Your Project"}),(0,T.jsx)("p",{className:"text-gray-400",children:"Tell us about your vision and we'll help bring it to life."})]}),(0,T.jsx)(wr,{children:(0,T.jsxs)("form",{onSubmit:async t=>{t.preventDefault(),a(!0),setTimeout((()=>{e({title:"Message Sent Successfully!",description:"We'll get back to you within 24 hours.",action:(0,T.jsx)(du,{className:"w-4 h-4 text-green-500"})}),n({name:"",email:"",company:"",projectType:"",budget:"",message:"",timeline:""}),a(!1)}),1500)},className:"space-y-6",children:[(0,T.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,T.jsxs)("div",{children:[(0,T.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Full Name *"}),(0,T.jsx)(ol,{required:!0,value:t.name,onChange:e=>l("name",e.target.value),className:"bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500 focus:border-yellow-400 focus:ring-yellow-400/20",placeholder:"John Doe"})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Email Address *"}),(0,T.jsx)(ol,{type:"email",required:!0,value:t.email,onChange:e=>l("email",e.target.value),className:"bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500 focus:border-yellow-400 focus:ring-yellow-400/20",placeholder:"<EMAIL>"})]})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Company Name"}),(0,T.jsx)(ol,{value:t.company,onChange:e=>l("company",e.target.value),className:"bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500 focus:border-yellow-400 focus:ring-yellow-400/20",placeholder:"Your Company"})]}),(0,T.jsxs)("div",{className:"grid md:grid-cols-3 gap-4",children:[(0,T.jsxs)("div",{children:[(0,T.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Project Type *"}),(0,T.jsxs)(ou,{onValueChange:e=>l("projectType",e),required:!0,children:[(0,T.jsx)(lu,{className:"bg-gray-800/50 border-gray-700 text-white focus:border-yellow-400 focus:ring-yellow-400/20",children:(0,T.jsx)(au,{placeholder:"Select type"})}),(0,T.jsx)(cu,{className:"bg-gray-800 border-gray-700",children:["AI-Integrated Website","CRM System","Data Science MVP","Mobile Application","Web Services & APIs","E-commerce Platform","Educational Software","Custom AI Solution"].map((e=>(0,T.jsx)(uu,{value:e,className:"text-white hover:bg-gray-700",children:e},e)))})]})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Budget Range"}),(0,T.jsxs)(ou,{onValueChange:e=>l("budget",e),children:[(0,T.jsx)(lu,{className:"bg-gray-800/50 border-gray-700 text-white focus:border-yellow-400 focus:ring-yellow-400/20",children:(0,T.jsx)(au,{placeholder:"Select budget"})}),(0,T.jsx)(cu,{className:"bg-gray-800 border-gray-700",children:["$10K - $25K","$25K - $50K","$50K - $100K","$100K - $250K","$250K+"].map((e=>(0,T.jsx)(uu,{value:e,className:"text-white hover:bg-gray-700",children:e},e)))})]})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Timeline"}),(0,T.jsxs)(ou,{onValueChange:e=>l("timeline",e),children:[(0,T.jsx)(lu,{className:"bg-gray-800/50 border-gray-700 text-white focus:border-yellow-400 focus:ring-yellow-400/20",children:(0,T.jsx)(au,{placeholder:"Select timeline"})}),(0,T.jsx)(cu,{className:"bg-gray-800 border-gray-700",children:["1-3 months","3-6 months","6-12 months","12+ months"].map((e=>(0,T.jsx)(uu,{value:e,className:"text-white hover:bg-gray-700",children:e},e)))})]})]})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Project Description *"}),(0,T.jsx)(ll,{required:!0,value:t.message,onChange:e=>l("message",e.target.value),rows:6,className:"bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500 focus:border-yellow-400 focus:ring-yellow-400/20",placeholder:"Tell us about your project goals, requirements, and any specific features you have in mind..."})]}),(0,T.jsx)(nr,{type:"submit",disabled:o,className:"w-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-black hover:from-yellow-300 hover:to-yellow-500 font-medium py-3 text-lg disabled:opacity-50",children:o?(0,T.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,T.jsx)("div",{className:"w-5 h-5 border-2 border-black/30 border-t-black rounded-full animate-spin"}),(0,T.jsx)("span",{children:"Sending..."})]}):(0,T.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,T.jsx)(hu,{className:"w-5 h-5"}),(0,T.jsx)("span",{children:"Send Message"})]})})]})})]})})]})]})})},vu=Pt("twitter",[["path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z",key:"pff0z6"}]]),yu=Pt("linkedin",[["path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z",key:"c2jq9f"}],["rect",{width:"4",height:"12",x:"2",y:"9",key:"mk3on5"}],["circle",{cx:"4",cy:"4",r:"2",key:"bt5ra8"}]]),bu=Pt("github",[["path",{d:"M15 22v-4a4.8 4.8 0 0 0-1-3.5c3 0 6-2 6-5.5.08-1.25-.27-2.48-1-3.5.28-1.15.28-2.35 0-3.5 0 0-1 0-3 1.5-2.64-.5-5.36-.5-8 0C6 2 5 2 5 2c-.3 1.15-.3 2.35 0 3.5A5.403 5.403 0 0 0 4 9c0 3.5 3 5.5 6 5.5-.39.49-.68 1.05-.85 1.65-.17.6-.22 1.23-.15 1.85v4",key:"tonef"}],["path",{d:"M9 18c-4.51 2-5-2-7-2",key:"9comsn"}]]),wu=Pt("arrow-up",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]),xu=()=>(0,T.jsx)("footer",{className:"bg-black border-t border-gray-800/50",children:(0,T.jsxs)("div",{className:"container mx-auto px-6",children:[(0,T.jsx)("div",{className:"py-16",children:(0,T.jsxs)("div",{className:"grid lg:grid-cols-5 gap-12",children:[(0,T.jsxs)("div",{className:"lg:col-span-2",children:[(0,T.jsxs)("div",{className:"flex items-center space-x-3 mb-6",children:[(0,T.jsx)("img",{src:"https://customer-assets.emergentagent.com/job_futurehome/artifacts/ctjqr19b_Genrec_Mini_Logo-removebg-preview.png",alt:"Genrec AI",className:"w-12 h-12"}),(0,T.jsx)("img",{src:"https://customer-assets.emergentagent.com/job_futurehome/artifacts/0fkp6gzh_Genrec_Full_Logo-removebg-preview.png",alt:"GENREC",className:"h-10"})]}),(0,T.jsx)("p",{className:"text-gray-400 leading-relaxed mb-6 max-w-md",children:"At Genrec AI, we craft intelligent ecosystems that evolve, adapt, and anticipate. Bridging the gap between human creativity and machine intelligence."}),(0,T.jsxs)("div",{className:"flex space-x-4",children:[(0,T.jsx)("a",{href:"#",className:"w-10 h-10 bg-gray-800/50 rounded-lg flex items-center justify-center hover:bg-yellow-400/20 hover:text-yellow-400 transition-all duration-200 group",children:(0,T.jsx)(vu,{className:"w-5 h-5 text-gray-400 group-hover:text-yellow-400"})}),(0,T.jsx)("a",{href:"#",className:"w-10 h-10 bg-gray-800/50 rounded-lg flex items-center justify-center hover:bg-yellow-400/20 hover:text-yellow-400 transition-all duration-200 group",children:(0,T.jsx)(yu,{className:"w-5 h-5 text-gray-400 group-hover:text-yellow-400"})}),(0,T.jsx)("a",{href:"#",className:"w-10 h-10 bg-gray-800/50 rounded-lg flex items-center justify-center hover:bg-yellow-400/20 hover:text-yellow-400 transition-all duration-200 group",children:(0,T.jsx)(bu,{className:"w-5 h-5 text-gray-400 group-hover:text-yellow-400"})}),(0,T.jsx)("a",{href:"#",className:"w-10 h-10 bg-gray-800/50 rounded-lg flex items-center justify-center hover:bg-yellow-400/20 hover:text-yellow-400 transition-all duration-200 group",children:(0,T.jsx)(fu,{className:"w-5 h-5 text-gray-400 group-hover:text-yellow-400"})})]})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Quick Links"}),(0,T.jsx)("ul",{className:"space-y-3",children:[{label:"Home",href:"#home"},{label:"About",href:"#about"},{label:"Services",href:"#services"},{label:"Projects",href:"#projects"},{label:"Contact",href:"#contact"}].map((e=>(0,T.jsx)("li",{children:(0,T.jsx)("a",{href:e.href,className:"text-gray-400 hover:text-yellow-400 transition-colors duration-200 text-sm",children:e.label})},e.label)))})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Services"}),(0,T.jsx)("ul",{className:"space-y-3",children:["AI-Integrated Websites","CRM Systems","Data Science MVPs","Mobile Applications","Web Services & APIs"].map((e=>(0,T.jsx)("li",{children:(0,T.jsx)("span",{className:"text-gray-400 text-sm cursor-pointer hover:text-yellow-400 transition-colors duration-200",children:e})},e)))})]}),(0,T.jsxs)("div",{children:[(0,T.jsx)("h3",{className:"text-white font-semibold mb-4",children:"Stay Updated"}),(0,T.jsx)("p",{className:"text-gray-400 text-sm mb-4",children:"Subscribe to our newsletter for the latest AI insights and updates."}),(0,T.jsxs)("div",{className:"space-y-3",children:[(0,T.jsx)(ol,{type:"email",placeholder:"Enter your email",className:"bg-gray-800/50 border-gray-700 text-white placeholder:text-gray-500 focus:border-yellow-400 focus:ring-yellow-400/20"}),(0,T.jsx)(nr,{className:"w-full bg-gradient-to-r from-yellow-400 to-yellow-600 text-black hover:from-yellow-300 hover:to-yellow-500 font-medium",children:"Subscribe"})]})]})]})}),(0,T.jsx)("div",{className:"py-6 border-t border-gray-800/50",children:(0,T.jsxs)("div",{className:"flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0",children:[(0,T.jsxs)("div",{className:"flex flex-col md:flex-row items-center space-y-2 md:space-y-0 md:space-x-6",children:[(0,T.jsx)("p",{className:"text-gray-400 text-sm",children:"\xa9 2025 Genrec AI. All rights reserved."}),(0,T.jsxs)("div",{className:"flex space-x-6",children:[(0,T.jsx)("a",{href:"#",className:"text-gray-400 hover:text-yellow-400 text-sm transition-colors duration-200",children:"Privacy Policy"}),(0,T.jsx)("a",{href:"#",className:"text-gray-400 hover:text-yellow-400 text-sm transition-colors duration-200",children:"Terms of Service"}),(0,T.jsx)("a",{href:"#",className:"text-gray-400 hover:text-yellow-400 text-sm transition-colors duration-200",children:"Cookie Policy"})]})]}),(0,T.jsxs)(nr,{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},variant:"outline",size:"sm",className:"border-gray-700 text-gray-400 hover:bg-yellow-400/10 hover:border-yellow-400/30 hover:text-yellow-400",children:[(0,T.jsx)(wu,{className:"w-4 h-4 mr-2"}),"Back to Top"]})]})})]})});const ku=function(){return(0,T.jsxs)("div",{className:"App",children:[(0,T.jsx)(or,{}),(0,T.jsxs)("main",{children:[(0,T.jsx)(ur,{}),(0,T.jsx)(kr,{}),(0,T.jsx)(zr,{}),(0,T.jsx)(nl,{}),(0,T.jsx)(gu,{})]}),(0,T.jsx)(xu,{}),(0,T.jsx)(Jn,{})]})};a.createRoot(document.getElementById("root")).render((0,T.jsx)(r.StrictMode,{children:(0,T.jsx)(ku,{})}))})();
//# sourceMappingURL=main.4254e8f7.js.map