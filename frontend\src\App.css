@import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@400;500;700;900&family=Inter:wght@300;400;500;600;700&display=swap');

.App {
  min-height: 100vh;
  background-color: #000000;
}

/* Smooth scrolling for anchor links */
html {
  scroll-behavior: smooth;
}

/* Custom font classes */
.font-orbitron {
  font-family: 'Orbitron', monospace;
}

.font-inter {
  font-family: 'Inter', sans-serif;
}

/* Apply fonts globally */
body {
  font-family: 'Inter', sans-serif;
}

h1, h2, h3, h4, h5, h6 {
  font-family: 'Orbitron', monospace;
}

/* Custom animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(250, 204, 21, 0.3);
  }
  50% { 
    box-shadow: 0 0 30px rgba(250, 204, 21, 0.5), 0 0 40px rgba(250, 204, 21, 0.3);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1a1a1a;
}

::-webkit-scrollbar-thumb {
  background: #facc15;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #eab308;
}

/* Selection styles */
::selection {
  background: rgba(250, 204, 21, 0.3);
  color: white;
}

/* Custom grid for particle effects */
.grid-cols-20 {
  grid-template-columns: repeat(20, minmax(0, 1fr));
}

.grid-rows-20 {
  grid-template-rows: repeat(20, minmax(0, 1fr));
}

/* Glassmorphism effect */
.glassmorphism {
  background: rgba(17, 24, 39, 0.25);
  backdrop-filter: blur(16px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.125);
}

/* Hover effects for cards */
.hover-lift {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.hover-lift:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

/* Custom gradient text */
.gradient-text {
  background: linear-gradient(135deg, #facc15 0%, #eab308 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Loading spinner */
@keyframes spin {
  to { transform: rotate(360deg); }
}

.animate-spin-slow {
  animation: spin 3s linear infinite;
}

/* Focus styles for accessibility */
button:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid #facc15;
  outline-offset: 2px;
}

/* Custom button hover effects */
.btn-glow:hover {
  box-shadow: 0 0 20px rgba(250, 204, 21, 0.4);
}

/* Responsive typography */
@media (max-width: 768px) {
  .text-responsive {
    font-size: clamp(1.5rem, 4vw, 3rem);
  }
}

/* Dark mode compatibility */
@media (prefers-color-scheme: dark) {
  .App {
    background-color: #000000;
    color: #ffffff;
  }
}